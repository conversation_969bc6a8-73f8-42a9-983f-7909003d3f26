replicaCount: 1

image:
  # override docker image tag, which defaults to appVersion defined in Chart.yaml
  # tag: 10-beta.1

  repository: 639966507663.dkr.ecr.eu-west-2.amazonaws.com/quest-policy-integration-service
  pullPolicy: IfNotPresent

nameOverride: ""
fullnameOverride: "submission-adapter-service"

service:
  type: ClusterIP
  port: 80

app:
  containerPort: 8080
  envs: |
  #    - name: service.policies-search.url
  #      value: http://policies-search-service/policies-search/api/v1
  secretWithEnvs:
    create: false # please consider that creating secret is possible if secreted with name provided does not already exists
    name: "" # when name is not provided, deployment name is used
    envs: { }
    # ENV_NAME:
    #   key: key
    #   value: value

livenessProbe:
   enabled: true
   path: /manage/health
   initialDelaySeconds: 100 #60
   periodSeconds: 10
   timeoutSeconds: 15 #5
   successThreshold: 1
   failureThreshold: 25 #3

readinessProbe:
   enabled: true
   path: /manage/health
   initialDelaySeconds: 100 #30
   periodSeconds: 10
   timeoutSeconds: 15 #3
   successThreshold: 1
   failureThreshold: 20 #3

resources:
  limits:
    cpu: '16'
    memory: 16Gi
  requests:
    cpu: '2'
    memory: 2Gi

nodeSelector: 
  role: blast-zone-creator

tolerations:
- key: "product"
  operator: "Equal"
  value: "blast-zone-creator"
  effect: "NoSchedule"

affinity: 
  enabled: false
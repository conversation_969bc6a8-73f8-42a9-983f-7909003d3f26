package com.concirrus.blast_zone_creator.controller;

import com.concirrus.blast_zone_creator.service.MiAnalysisService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MiAnalysisControllerTest {

    @Mock
    private MiAnalysisService miAnalysisService;

    @InjectMocks
    private MiAnalysisController controller;

    @Test
    void getMiAnalysisJob_shouldReturn200_whenJobExists() {
        // Given
        String submissionId = "validSubmissionId";
        String quoteId = "validQuoteId";
        String clientId = "validClientId";
        Map<String, String> mockResult = new HashMap<>();
        mockResult.put("jobId", "job123");
        mockResult.put("status", "COMPLETED");
        
        when(miAnalysisService.getJobForAccount(submissionId, quoteId)).thenReturn(mockResult);

        // When
        var response = controller.getMiAnalysisJob(clientId,submissionId, quoteId).getBody();

        // Then
        assertEquals(HttpStatus.OK.value(), response.getStatus());
        assertEquals(mockResult, response.getResult());
        assertNull(response.getError());
    }

    @Test
    void getMiAnalysisJob_shouldReturn404_whenJobNotFound() {
        // Given
        String submissionId = "invalidSubmissionId";
        String quoteId = "invalidQuoteId";
        String clientId = "validClientId";
        
        when(miAnalysisService.getJobForAccount(submissionId, quoteId)).thenReturn(null);

        // When
        var response = controller.getMiAnalysisJob(clientId,submissionId, quoteId).getBody();

        // Then
        assertEquals(HttpStatus.NOT_FOUND.value(), response.getStatus());
        assertNull(response.getResult());
        assertEquals("Job not found for the given submissionId and quoteId", response.getError());
    }
}
server:
  port: 8080
  servlet:
    session:
      timeout: 180m

management:
  endpoints:
    web:
      base-path: /manage
      exposure:
        include: health, info, prometheus, restart
  endpoint:
    info:
      enabled: true
    health:
      enabled: true
      show-details: always
    restart:
      enabled: true

cloud:
  gcp:
    project-id: prj-nonprod-eng-svc-01
  provider: aws
  aws:
    credentials:
      access-key:
      secret-key:
    region:
      static: us-east-2
  messaging:
    subscription:
      location-update-queue: ${BLAST_ZONE_CREATOR_SUBSCRIPTION:blastzone-creation-dev-sub}
    topic:
      internal-queue: ${BLAST_ZONE_PROCESSOR_INTERNAL_TOPIC:blastzone-creation-dev}
      task-callback: ${WORKFLOW_CALLBACK_TOPIC:submission-adapter-callback-dev}


spring:
  data:
    mongodb:
      uri: mongodb://${AGGREGATION_MONGO_USERNAME:submission}:${AGGREGATION_MONGO_PASSWORD}@${AGGREGATION_MONGO_IPS:localhost:27017}/${AGGREGATION_DB:aggregation_db}?authSource=admin
      auto-index-creation: true
  cloud:
    gcp:
      pubsub:
        enabled: true

  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 10MB

geocoding:
  service:
    url: ${GEOCODING_SERVICE_URL:http://platform-geocoding-service}
    namespace: ${SUBMISSION_NAMESPACE:submission-dev}
    use-mock-data: ${USE_MOCK_DATA:true}

namespace:
  submission: ${SUBMISSION_NAMESPACE:submission}

tenant-ids: ${VALID_TENANT_IDS:26a926c5-2e2d-40bf-950b-1dddf41a3746}

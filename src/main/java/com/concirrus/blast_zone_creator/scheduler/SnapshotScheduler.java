package com.concirrus.blast_zone_creator.scheduler;

import com.concirrus.blast_zone_creator.config.TenantContextHolder;
import com.concirrus.blast_zone_creator.dal.SnapshotJobRepository;
import com.concirrus.blast_zone_creator.model.SnapshotJob;
import com.concirrus.blast_zone_creator.service.SnapshotService;
import com.concirrus.blast_zone_creator.service.TenantService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Snapshot Scheduler with Multitenant Support
 *
 * ⚠️ IMPORTANT: Scheduled tasks run without HTTP request context, so tenant context
 * must be set manually for each valid tenant to ensure proper database routing.
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class SnapshotScheduler {

    private final SnapshotJobRepository snapshotJobRepository;
    private final SnapshotService snapshotService;
    private final TenantService tenantService;

    // Runs at 12:00 AM on the 1st day of each month
    @Scheduled(cron = "0 0 0 1 * *")
    public void runMonthlyTask() {
        log.info("Running monthly snapshot job creation task at {}", LocalDateTime.now());

        // Get all valid tenant IDs and create snapshot jobs for each tenant
        var validTenantIds = tenantService.getValidTenantIds();

        for (String tenantId : validTenantIds) {
            try {
                // Set tenant context for each tenant
                String tenantAlias = tenantService.getTenantAlias(tenantId);
                TenantContextHolder.setTenantContext(tenantId, tenantAlias);

                log.info("Creating snapshot jobs for tenant: {} (alias: {})", tenantId, tenantAlias);
                snapshotService.createSnapshotJobs();
                log.info("Snapshot jobs created for tenant: {}", tenantId);

            } catch (Exception e) {
                log.error("Error creating snapshot jobs for tenant: {}", tenantId, e);
            } finally {
                // Always clean up tenant context
                TenantContextHolder.clear();
            }
        }

        log.info("Monthly snapshot job creation completed for all tenants");
    }

    // Runs every day at 10:00 AM and 10:00 PM
    @Scheduled(cron = "0 0 10,22 * * *")
    public void executeSnapshotJob() {
        log.info("Running snapshot execution task at {}", LocalDateTime.now());
        LocalDate today = LocalDate.now();

        // Get all valid tenant IDs and execute snapshot jobs for each tenant
        var validTenantIds = tenantService.getValidTenantIds();

        for (String tenantId : validTenantIds) {
            try {
                // Set tenant context for each tenant
                String tenantAlias = tenantService.getTenantAlias(tenantId);
                TenantContextHolder.setTenantContext(tenantId, tenantAlias);

                log.info("Executing snapshot jobs for tenant: {} (alias: {})", tenantId, tenantAlias);

                List<SnapshotJob> jobs = snapshotJobRepository.findByExecutionDate(today);
                if (jobs == null || jobs.isEmpty()) {
                    log.info("No snapshot jobs to execute for tenant: {} on {}", tenantId, today);
                } else {
                    log.info("Found {} snapshot jobs for tenant: {} on {}", jobs.size(), tenantId, today);
                    jobs.forEach(snapshotService::createSnapshot);
                    log.info("Executed {} snapshot jobs for tenant: {}", jobs.size(), tenantId);
                }

            } catch (Exception e) {
                log.error("Error executing snapshot jobs for tenant: {}", tenantId, e);
            } finally {
                // Always clean up tenant context
                TenantContextHolder.clear();
            }
        }

        log.info("Snapshot execution completed for all tenants");
    }


}

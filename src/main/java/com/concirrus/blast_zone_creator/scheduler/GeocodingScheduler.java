package com.concirrus.blast_zone_creator.scheduler;

import com.concirrus.blast_zone_creator.config.TenantContextHolder;
import com.concirrus.blast_zone_creator.dal.blastzone.BlastZoneDAL;
import com.concirrus.blast_zone_creator.dal.blastzone.BlastZoneRepository;
import com.concirrus.blast_zone_creator.model.blastzone.BlastZone;
import com.concirrus.blast_zone_creator.service.ReverseGeocodingService;
import com.concirrus.blast_zone_creator.service.RiskDataService;
import com.concirrus.blast_zone_creator.service.TenantService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Geocoding Scheduler with Multitenant Support
 *
 * ⚠️ IMPORTANT: Scheduled tasks run without HTTP request context, so tenant context
 * must be set manually for each valid tenant to ensure proper database routing.
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class GeocodingScheduler {
    private final BlastZoneDAL blastZoneDAL;
    private final BlastZoneRepository blastZoneRepository;
    private final ReverseGeocodingService reverseGeocodingService;
    private final RiskDataService riskDataService;
    private final TenantService tenantService;
    private static final int BATCH_SIZE = 100;

    @Scheduled(initialDelay = 60000, fixedRate = 120000) // every 2 minutes
    public void enrichBlastZones() {
        log.info("Starting geocoding task for all tenants");

        // Get all valid tenant IDs and process geocoding for each tenant
        var validTenantIds = tenantService.getValidTenantIds();

        for (String tenantId : validTenantIds) {
            try {
                // Set tenant context for each tenant
                String tenantAlias = tenantService.getTenantAlias(tenantId);
                TenantContextHolder.setTenantContext(tenantId, tenantAlias);

                log.info("Starting geocoding for tenant: {} (alias: {})", tenantId, tenantAlias);

                List<BlastZone> blastZones = blastZoneDAL.getBlastZonesNeedingGeocoding(BATCH_SIZE);

                if (blastZones.isEmpty()) {
                    log.info("No blast zones needing geocoding for tenant: {}", tenantId);
                    continue;
                }

                log.info("Found {} blast zones needing geocoding for tenant: {}", blastZones.size(), tenantId);

                // Process geocoding
                reverseGeocodingService.geocodeBlastZones(blastZones);
                log.info("Geocoding completed for {} blast zones for tenant: {}", blastZones.size(), tenantId);

                // Enrich with risk data
                log.info("Enriching blast zones with risk data for tenant: {}", tenantId);
                boolean success = riskDataService.enrichBlastZonesWithRiskData(blastZones);
                if (!success) {
                    log.warn("Failed to enrich blast zones with risk data for tenant: {}", tenantId);
                } else {
                    log.info("Successfully enriched blast zones with risk data for tenant: {}", tenantId);
                }

                // Save enriched blast zones
                blastZoneRepository.saveAll(blastZones);
                log.info("Geocoding task completed for tenant: {}", tenantId);

            } catch (Exception e) {
                log.error("Error during geocoding task for tenant: {}", tenantId, e);
            } finally {
                // Always clean up tenant context
                TenantContextHolder.clear();
            }
        }

        log.info("Geocoding task completed for all tenants");
    }
}

package com.concirrus.blast_zone_creator.scheduler;

import com.concirrus.blast_zone_creator.config.TenantContextHolder;
import com.concirrus.blast_zone_creator.service.RecomputationService;
import com.concirrus.blast_zone_creator.service.TenantService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * Daily Task Scheduler with Multitenant Support
 *
 * ⚠️ IMPORTANT: Scheduled tasks run without HTTP request context, so tenant context
 * must be set manually for each valid tenant to ensure proper database routing.
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class DailyTaskScheduler {
    private final RecomputationService recomputationService;
    private final TenantService tenantService;

    @Scheduled(cron = "0 0 0 * * *") // Run at midnight every day
    public void runDailyTasks() {
        log.info("Running daily tasks for all tenants");

        // Get all valid tenant IDs and process each tenant separately
        var validTenantIds = tenantService.getValidTenantIds();

        for (String tenantId : validTenantIds) {
            try {
                // Set tenant context for each tenant
                String tenantAlias = tenantService.getTenantAlias(tenantId);
                TenantContextHolder.setTenantContext(tenantId, tenantAlias);

                log.info("Running daily tasks for tenant: {} (alias: {})", tenantId, tenantAlias);
                recomputationService.recomputeFirstExpiryForBlastZones();
                log.info("Daily tasks completed for tenant: {}", tenantId);

            } catch (Exception e) {
                log.error("Error running daily tasks for tenant: {}", tenantId, e);
            } finally {
                // Always clean up tenant context
                TenantContextHolder.clear();
            }
        }

        log.info("Daily tasks completed for all tenants");
    }
}

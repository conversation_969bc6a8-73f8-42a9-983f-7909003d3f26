package com.concirrus.blast_zone_creator.sal;

import com.concirrus.blast_zone_creator.dto.submission.BasicResponse;
import com.concirrus.blast_zone_creator.dto.submission.ClientConfigResponse;
import com.concirrus.blast_zone_creator.dto.submission.TokenResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import static com.concirrus.blast_zone_creator.constants.Constants.ACCESS_TOKEN_CACHE;
import static com.concirrus.blast_zone_creator.constants.Constants.CLIENT_CONFIG_CACHE;


@Component
@Slf4j
public class AccessManagementSal {
    private final RestTemplate restTemplate;
    private final String submissionNamespace;
    private static final String BASE_URL = "http://access-management-service.";
    private static final String GET_INTERNAL_TOKEN_PATH = "/access-management/api/v1/access-management/token";
    private static final String GET_CLIENT_CONFIG_PATH = "/access-management/api/v1/access-management";
    private static final String GET_ALL_CLIENT_CONFIG_PATH = "/access-management/api/v1/access-management/clients";

    public AccessManagementSal(RestTemplate restTemplate, @Value("${namespace.submission}") String submissionNamespace) {
        this.restTemplate = restTemplate;
        this.submissionNamespace = submissionNamespace;
    }

    @Cacheable(value = CLIENT_CONFIG_CACHE, key = "#clientId")
    public ClientConfigResponse getClientConfigByClientId(String clientId) {
        validateClientId(clientId);
        String baseUrl = BASE_URL + submissionNamespace + GET_CLIENT_CONFIG_PATH;
        String url = String.format("%s/client/%s", baseUrl, clientId);

        try {
            ResponseEntity<BasicResponse<ClientConfigResponse>> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    null,
                    new ParameterizedTypeReference<BasicResponse<ClientConfigResponse>>() {}
            );
            return extractClientConfigFromResponse(response, clientId);
        } catch (HttpClientErrorException | HttpServerErrorException ex) {
            log.error("HTTP error while fetching client config for client {}: {} - {}",
                    clientId, ex.getStatusCode(), ex.getResponseBodyAsString());
            throw new AccessManagementException("Failed to fetch client config due to HTTP error: " + ex.getStatusCode(), ex);

        } catch (RestClientException ex) {
            log.error("Network error while calling access management service for client config {}: {}",
                    clientId, ex.getMessage());
            throw new AccessManagementException("Network error while calling access management service", ex);
        }
    }

    @Cacheable(value = ACCESS_TOKEN_CACHE, key = "#clientId")
    public String getToken(String clientId) {
        validateClientId(clientId);
        String url = BASE_URL + submissionNamespace + GET_INTERNAL_TOKEN_PATH;

        try {
            log.info("Getting token for clientId: {}", clientId);

            HttpHeaders headers = new HttpHeaders();
            headers.set("client-id", clientId);

            ResponseEntity<BasicResponse<TokenResponse>> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    new HttpEntity<>(headers),
                    new ParameterizedTypeReference<BasicResponse<TokenResponse>>() {}
            );

            return extractTokenFromResponse(response, clientId);

        } catch (HttpClientErrorException | HttpServerErrorException ex) {
            log.error("HTTP error while fetching token for client {}: {} - {}",
                    clientId, ex.getStatusCode(), ex.getResponseBodyAsString());
            throw new AccessManagementException("Failed to fetch token due to HTTP error: " + ex.getStatusCode(), ex);

        } catch (RestClientException ex) {
            log.error("Network error while calling access management service for client {}: {}",
                    clientId, ex.getMessage());
            throw new AccessManagementException("Network error while calling access management service", ex);
        }
    }

    private void validateClientId(String clientId) {
        if (clientId == null || clientId.trim().isEmpty()) {
            throw new IllegalArgumentException("Client ID cannot be null or empty");
        }
    }

    private String extractTokenFromResponse(ResponseEntity<BasicResponse<TokenResponse>> response, String clientId) {
        if (response.getStatusCode() != HttpStatus.OK) {
            log.warn("Unexpected status code {} when fetching token for client {}", response.getStatusCode(), clientId);
            throw new AccessManagementException("Unexpected response status: " + response.getStatusCode());
        }

        BasicResponse<TokenResponse> responseBody = response.getBody();
        if (responseBody == null) {
            log.error("Response body is null for client token {}", clientId);
            throw new AccessManagementException("Received null response body from access management service");
        }

        TokenResponse tokenResponse = responseBody.getResult();
        if (tokenResponse == null) {
            log.error("Token response is null for client {}", clientId);
            throw new AccessManagementException("Token response is null");
        }

        String accessToken = tokenResponse.getAccessToken();
        if (accessToken == null || accessToken.trim().isEmpty()) {
            log.error("Access token is null or empty for client {}", clientId);
            throw new AccessManagementException("Access token is null or empty");
        }

        log.info("Successfully retrieved token for client {}", clientId);
        return accessToken;
    }
    private ClientConfigResponse extractClientConfigFromResponse(ResponseEntity<BasicResponse<ClientConfigResponse>> response, String clientId) {
        if (response.getStatusCode() != HttpStatus.OK) {
            log.warn("Unexpected status code {} when fetching client config for client {}", response.getStatusCode(), clientId);
            throw new AccessManagementException("Unexpected response status: " + response.getStatusCode());
        }

        BasicResponse<ClientConfigResponse> responseBody = response.getBody();
        if (responseBody == null) {
            log.error("Response body is null for client config {}", clientId);
            throw new AccessManagementException("Received null response body from access management service");
        }

        ClientConfigResponse clientConfigResponse = responseBody.getResult();
        if (clientConfigResponse == null) {
            log.error("client config response is null for client {}", clientId);
            throw new AccessManagementException("Token response is null");
        }

        log.info("Successfully retrieved client config for client {}", clientId);
        return  clientConfigResponse;
    }

    // Custom exception for better error handling
    public static class AccessManagementException extends RuntimeException {
        public AccessManagementException(String message) {
            super(message);
        }

        public AccessManagementException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
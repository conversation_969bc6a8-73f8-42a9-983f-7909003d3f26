package com.concirrus.blast_zone_creator.sal;

import com.concirrus.blast_zone_creator.dto.geocoding.GeocodingRequest;
import com.concirrus.blast_zone_creator.dto.geocoding.GeocodingResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor
public class GeocodingSal {

    public static final String DOT = ".";
    private final RestTemplate restTemplate;

    @Value("${geocoding.service.url}")
    private final String geocodingServiceUrl;

    @Value("${geocoding.service.namespace}")
    private final String geocodingServiceNamespace;
    
    private static final String BATCH_REVERSE_GEOCODING_PATH = "/api/v1/geocoding/reverse/batch";
    

    /**
     * Performs batch reverse geocoding for a list of coordinates
     * 
     * @param requests List of geocoding requests containing coordinates
     * @return List of geocoding responses with address information
     * @throws GeocodingException if the geocoding service call fails
     */
    public List<GeocodingResponse> batchReverseGeocode(List<GeocodingRequest> requests) {
        if (requests == null || requests.isEmpty()) {
            log.warn("Empty or null geocoding requests provided");
            throw new IllegalArgumentException("Geocoding requests cannot be null or empty");
        }
        
        String url = geocodingServiceUrl+ DOT + geocodingServiceNamespace + BATCH_REVERSE_GEOCODING_PATH;
        
        try {
            log.info("Calling batch reverse geocoding service for {} coordinates", requests.size());
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<List<GeocodingRequest>> entity = new HttpEntity<>(requests, headers);
            
            ResponseEntity<List<GeocodingResponse>> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    entity,
                    new ParameterizedTypeReference<List<GeocodingResponse>>() {}
            );
            
            return extractGeocodingResponseFromResponse(response, requests.size());
            
        } catch (HttpClientErrorException | HttpServerErrorException ex) {
            log.error("HTTP error while calling geocoding service: {} - {}", 
                    ex.getStatusCode(), ex.getResponseBodyAsString());
            throw new GeocodingException("Failed to call geocoding service due to HTTP error: " + ex.getStatusCode(), ex);
            
        } catch (RestClientException ex) {
            log.error("Network error while calling geocoding service: {}", ex.getMessage());
            throw new GeocodingException("Network error while calling geocoding service", ex);
        }
    }
    
    private List<GeocodingResponse> extractGeocodingResponseFromResponse(
            ResponseEntity<List<GeocodingResponse>> response, int expectedCount) {
        
        if (response.getStatusCode() != HttpStatus.OK) {
            log.warn("Unexpected status code {} when calling geocoding service", response.getStatusCode());
            throw new GeocodingException("Unexpected response status: " + response.getStatusCode());
        }
        
        List<GeocodingResponse> responseBody = response.getBody();
        if (responseBody == null) {
            log.error("Response body is null from geocoding service");
            throw new GeocodingException("Received null response body from geocoding service");
        }
        
        log.info("Successfully received {} geocoding responses (expected: {})", 
                responseBody.size(), expectedCount);
        return responseBody;
    }
    
    // Custom exception for better error handling
    public static class GeocodingException extends RuntimeException {
        public GeocodingException(String message) {
            super(message);
        }
        
        public GeocodingException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}

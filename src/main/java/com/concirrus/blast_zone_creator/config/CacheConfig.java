package com.concirrus.blast_zone_creator.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.concirrus.blast_zone_creator.constants.Constants.*;

@Configuration
@EnableCaching
public class CacheConfig {
    private static final int EXPIRATION_TIME_IN_SECONDS = 600;

    @Bean
    public Caffeine caffeineConfig() {
        return Caffeine.newBuilder()
                .expireAfterWrite(EXPIRATION_TIME_IN_SECONDS, TimeUnit.SECONDS);
    }

    @Bean
    public CacheManager cacheManager(Caffeine caffeine) {
        CaffeineCacheManager caffeineCacheManager = new CaffeineCacheManager();
        caffeineCacheManager.setCacheNames(List.of(ACCESS_TOKEN_CACHE,CLIENT_CONFIG_CACHE,DAMAGE_FACTOR_CACHE));
        caffeineCacheManager.setCaffeine(caffeine);
        return caffeineCacheManager;
    }

}
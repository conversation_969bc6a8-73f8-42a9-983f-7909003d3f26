package com.concirrus.blast_zone_creator.config;

import com.concirrus.blast_zone_creator.utils.TenantIndexCreator;
import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.WriteConcern;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import com.mongodb.connection.ConnectionPoolSettings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.MongoTransactionManager;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;

import java.util.concurrent.TimeUnit;

/**
 * Multitenant MongoDB Configuration with Optimized Connection Pooling
 *
 * This configuration provides:
 * 1. Single optimized connection pool shared across all tenants
 * 2. Tenant-specific database routing via MultiTenantMongoDatabaseFactory
 * 3. High-performance settings for concurrent operations
 *
 * Connection Pool Strategy:
 * - One MongoClient with optimized pool serves ALL tenant databases
 * - Each tenant gets routed to database: aggregation_db_{tenantAlias}
 * - Connections are efficiently shared across tenants
 * - Pool settings: 200 max, 20 min connections for high throughput
 */
@Configuration
public class MultiTenantMongoConfig {

    @Bean
    public TenantIndexCreator tenantIndexCreator(MongoMappingContext mappingContext) {
        return new TenantIndexCreator(mappingContext);
    }

    @Value("${spring.data.mongodb.uri}")
    private String mongoUri;

    /**
     * Creates optimized MongoClient with connection pooling for multitenant use
     *
     * Pool Configuration:
     * - maxSize: 200 connections (increased for parallel tenant operations)
     * - minSize: 20 connections (warm pool ready for any tenant)
     * - maxWaitTime: 120s (sufficient for bulk operations)
     * - Connection lifecycle optimized for long-running processes
     */
    @Bean
    @Primary
    public MongoClient mongoClient() {
        ConnectionString connectionString = new ConnectionString(mongoUri);

        // Optimize connection pool for multitenant high throughput
        ConnectionPoolSettings poolSettings = ConnectionPoolSettings.builder()
                .maxSize(200) // Shared across all tenants - increased for parallel operations
                .minSize(20)  // Keep warm connections ready for any tenant
                .maxWaitTime(120, TimeUnit.SECONDS) // Allow time for bulk operations
                .maxConnectionLifeTime(0, TimeUnit.MINUTES) // Keep connections alive
                .maxConnectionIdleTime(10, TimeUnit.MINUTES) // Reasonable idle timeout
                .maintenanceFrequency(10, TimeUnit.SECONDS) // Regular pool maintenance
                .build();

        MongoClientSettings settings = MongoClientSettings.builder()
                .applyConnectionString(connectionString)
                .applyToConnectionPoolSettings(builder ->
                    builder.applySettings(poolSettings))
                // Server selection timeout for better resilience across tenants
                .applyToServerSettings(builder ->
                    builder.heartbeatFrequency(10, TimeUnit.SECONDS))
                // Cluster settings with explicit server selection timeout
                .applyToClusterSettings(builder ->
                    builder.serverSelectionTimeout(120, TimeUnit.SECONDS))
                // Socket settings optimized for multitenant performance
                .applyToSocketSettings(builder -> {
                    builder.connectTimeout(10, TimeUnit.SECONDS);
                    builder.readTimeout(120, TimeUnit.SECONDS); // Increased for large tenant operations
                })
                // Write concern optimized for bulk operations across tenants
                .writeConcern(WriteConcern.W1)
                .build();

        return MongoClients.create(settings);
    }

    /**
     * Multitenant database factory that routes to tenant-specific databases
     * Uses the shared optimized connection pool from mongoClient()
     */
    @Bean
    @Primary
    public MongoDatabaseFactory mongoDatabaseFactory(MongoClient mongoClient, TenantIndexCreator tenantIndexCreator) {
        return new MultiTenantMongoDatabaseFactory(mongoClient, tenantIndexCreator);
    }

    /**
     * MongoTemplate using multitenant database factory
     * Automatically routes operations to correct tenant database
     */
    @Bean
    @Primary
    public MongoTemplate mongoTemplate(MongoDatabaseFactory mongoDatabaseFactory) {
        return new MongoTemplate(mongoDatabaseFactory);
    }

    /**
     * Transaction manager for multitenant operations
     * Supports transactions within tenant-specific databases
     */
    @Bean
    @Primary
    public MongoTransactionManager transactionManager(MongoDatabaseFactory dbFactory) {
        return new MongoTransactionManager(dbFactory);
    }
}
package com.concirrus.blast_zone_creator.config;

import com.google.cloud.spring.pubsub.core.PubSubTemplate;
import com.google.cloud.spring.pubsub.integration.AckMode;
import com.google.cloud.spring.pubsub.integration.inbound.PubSubInboundChannelAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.messaging.MessageChannel;

@Configuration
@Slf4j
public class PubSubConfig {

    // Receives incoming location updates from the Pub/Sub subscription
    @Bean
    public DirectChannel locationInputChannel() {
        return new DirectChannel();
    }


    // Adapter for the external location update subscription
    @Bean
    public PubSubInboundChannelAdapter locationInputAdapter(
            MessageChannel locationInputChannel,
            PubSubTemplate pubSubTemplate,
            @Value("${cloud.messaging.subscription.location-update-queue}") String locationUpdateSubscription) {

        PubSubInboundChannelAdapter adapter = new PubSubInboundChannelAdapter(pubSubTemplate, locationUpdateSubscription);
        adapter.setOutputChannel(locationInputChannel);
        adapter.setAckMode(AckMode.MANUAL);
        adapter.setPayloadType(String.class);
        return adapter;
    }

}
package com.concirrus.blast_zone_creator.config;

import com.concirrus.blast_zone_creator.utils.TenantInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web Configuration for Multitenant Setup
 * 
 * Registers the TenantInterceptor to automatically extract client-id headers
 * and set tenant context for all incoming requests.
 * 
 * The interceptor will:
 * 1. Extract client-id from request headers
 * 2. Resolve tenant alias using TenantService
 * 3. Set tenant context in TenantContextHolder
 * 4. Route database operations to tenant-specific databases
 * 5. Clean up tenant context after request completion
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private TenantInterceptor tenantInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(tenantInterceptor)
                .excludePathPatterns("/manage/**"); // Exclude actuator endpoints
    }
}

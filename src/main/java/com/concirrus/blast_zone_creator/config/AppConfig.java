package com.concirrus.blast_zone_creator.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Configuration
public class AppConfig {

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    /**
     * Virtual Thread Executor with Tenant Context Support
     *
     * ⚠️ WARNING: Virtual threads don't automatically inherit tenant context.
     * When using this executor, ensure tenant context is manually set in each task.
     * Consider using the @Async methods with TenantAwareTaskDecorator instead.
     */
    @Bean
    @Qualifier("virtualThreadExecutor")
    public ExecutorService virtualThreadExecutor() {
        return Executors.newVirtualThreadPerTaskExecutor();
    }
}

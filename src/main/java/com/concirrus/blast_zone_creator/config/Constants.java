package com.concirrus.blast_zone_creator.config;

import org.springframework.data.geo.Distance;
import org.springframework.data.geo.Metrics;

public class Constants {
    public static final double PML_UTILIZATION_LIMIT =  65_000_000.0;
    public static final double EXPOSURE_UTILIZATION_LIMIT = 85_000_000.0;
    public static final Distance BLAST_ZONE_RADIUS  = new Distance(0.250, Metrics.KILOMETERS);
    public static final String BOUND_STATE = "BOUND";
    public static final String SEPARATOR = "#";
    public static final String BLAST_ZONE = "BlastZone";
    public static final String SRCC = "srcc";
    public static final String WAR = "war";
    public static final String SANDT = "sandt";
    public static final String S_AND_T = "S&T";
    public static final String GOOD = "Good";
    public static final String BAD = "Bad";
    public static final String IDEAL = "Ideal";
    public static final Double HUNDRED = 100.0;
    public static final Double ZERO = 0.0;
    public static final String RECOMPUTE_TASK = "RECOMPUTE_TASK";
}

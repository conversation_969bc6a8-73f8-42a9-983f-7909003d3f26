package com.concirrus.blast_zone_creator.config;

import com.concirrus.blast_zone_creator.utils.TenantIndexCreator;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoDatabase;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.SimpleMongoClientDatabaseFactory;

import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Multitenant MongoDB Database Factory with Connection Pool Optimization
 *
 * This factory routes database operations to tenant-specific databases while
 * using a shared, optimized connection pool for all tenants.
 *
 * Database Naming Convention:
 * - Default: aggregation_db
 * - Tenant: aggregation_db_{tenantAlias}
 *
 * Connection Pool Benefits:
 * - Single pool (200 max, 20 min connections) serves all tenants
 * - Efficient resource utilization across tenants
 * - Automatic load balancing by MongoDB driver
 * - Reduced connection overhead compared to per-tenant pools
 */
@Slf4j
public class MultiTenantMongoDatabaseFactory extends SimpleMongoClientDatabaseFactory {

    public static final String UNDER_SCORE = "_";
    private final MongoClient mongoClient;
    private static final String DEFAULT_DATABASE = "aggregation_db";
    private final TenantIndexCreator tenantIndexCreator;
    private final Set<String> initialized = ConcurrentHashMap.newKeySet();

    public MultiTenantMongoDatabaseFactory(MongoClient mongoClient, TenantIndexCreator tenantIndexCreator) {
        super(mongoClient, DEFAULT_DATABASE); // default database
        this.mongoClient = mongoClient;
        this.tenantIndexCreator = tenantIndexCreator;
        log.info("Initialized MultiTenantMongoDatabaseFactory with optimized connection pool");
        log.info("Default database: {}", DEFAULT_DATABASE);
    }

    /**
     * Routes to tenant-specific database using shared connection pool
     *
     * @return MongoDatabase instance for current tenant or default database
     */
    @NotNull
    @Override
    public MongoDatabase getMongoDatabase() {
        String tenantAlias = TenantContextHolder.getTenantAlias();
        String databaseName = (tenantAlias != null)
                ? DEFAULT_DATABASE + UNDER_SCORE + tenantAlias
                : DEFAULT_DATABASE;

        // Ensure indexes only once per DB
        if (initialized.add(databaseName)) {
            log.info("Ensuring indexes for database: {}", databaseName);
            MongoTemplate tenantTemplate = new MongoTemplate(mongoClient, databaseName);
            tenantIndexCreator.ensureIndexes(tenantTemplate);
        }

        return mongoClient.getDatabase(databaseName);
    }

    /**
     * Get the underlying MongoClient for monitoring purposes
     * @return the shared MongoClient with optimized connection pool
     */
    @NotNull
    public MongoClient getMongoClient() {
        return mongoClient;
    }
}
package com.concirrus.blast_zone_creator.config;

import org.springframework.context.annotation.Configuration;

@Configuration
public class LockProviderConfig {

//    @Bean
//    public LockProvider lockProvider(MongoDatabaseFactory mongoDatabaseFactory) {
//        MongoCollection<Document> collection = mongoDatabaseFactory
//                .getMongoDatabase()
//                .getCollection("blast_zone_shedlock");
//        return new MongoLockProvider(collection);
//    }
}
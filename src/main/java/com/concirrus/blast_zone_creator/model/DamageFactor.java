package com.concirrus.blast_zone_creator.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "damage_factor")
public class DamageFactor {

    private int startDistance;
    private int endDistance;
    private int biDamage;
    private int contentsDf;
    private int buildingDf;
    private String zone;
    private String peril;

}

package com.concirrus.blast_zone_creator.model;

import com.concirrus.blast_zone_creator.dto.enums.ProcessingStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.Instant;
import java.time.LocalDate;
@Document("snapshot_job")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SnapshotJob {

    @Id
    private String jobId;
    private LocalDate executionDate;
    private Instant createdAt;
    private Instant updatedAt;
    private ProcessingStatus status;
    private Integer timeTaken;
}

package com.concirrus.blast_zone_creator.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "account")
public class Account {
    @Id
    private String id;
    @Indexed
    private String quoteId;
    private String accountName;
    @Indexed
    private String submissionId;       // {submissionId}
    private String state;    // {state of submission QUOTED,IN_REVIEW,BOUND}
    private String policyReference;   // {First reference number from quote}
    private String binder;            // {coverType from submission}
    private String inceptionDate;     // {inceptionDate from quote}
    private String expiryDate;         // {expirationDate from quote}
    private Double tiv;                // {unsure about the calculation}
    private Double limit;              // {Limit from risk details converted to USD}
    private Double excess;             // {Excess from risk details converted to USD}
    private Double deductible;                // {unsure about the field}
    private Double line;               // {which line to use? signed, written, effective}
    private Double exposure;           // {Applied exposure from referencing section}
    private Double pml;           // {unsure}
    private Double premium;            // {unsure}
    private String policyCurrency;     // {currencyCode}// {quoteId}
    private List<String> perils;        // {list of perils}
}

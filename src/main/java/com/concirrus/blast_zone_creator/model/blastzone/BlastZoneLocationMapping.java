package com.concirrus.blast_zone_creator.model.blastzone;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "blast_zone_location_mapping")
@CompoundIndexes({
        @CompoundIndex(
                name = "submissionId_blastZoneId_idx",
                def = "{'submissionId': 1, 'blastZoneId': 1}"
        ),
        @CompoundIndex(
                name = "blastZoneId_submissionId_idx",
                def = "{'blastZoneId': 1, 'submissionId': 1}"
        )
})
public class BlastZoneLocationMapping {

    @Id
    private String id;

    @Indexed
    private Long blastZoneId;

    @Indexed
    private String locationId;

    @Indexed
    private String submissionId;

    private double distance;
    private String geocodingGrade;
    private double blastZonePml;// Probable Maximum Loss
    private double blastZoneTiv;
    private String pmlZone;
    private double pmlPercentage;
    private String state;
}
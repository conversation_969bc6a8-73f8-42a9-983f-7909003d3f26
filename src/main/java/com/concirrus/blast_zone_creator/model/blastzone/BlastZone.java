package com.concirrus.blast_zone_creator.model.blastzone;

import com.concirrus.blast_zone_creator.dto.RiskDataDTO;
import lombok.*;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.geo.GeoJsonPoint;
import org.springframework.data.mongodb.core.index.GeoSpatialIndexType;
import org.springframework.data.mongodb.core.index.GeoSpatialIndexed;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.Instant;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
@Document("blast_zones")
public class BlastZone {
    @Id
    @Field("_id")
    private Long id;
    private String name;
    private String country;
    private String city;
    private String fullAddress;
    private String postalCode;
    @GeoSpatialIndexed(type = GeoSpatialIndexType.GEO_2DSPHERE)
    private GeoJsonPoint centre;
    private boolean active;
    private Double tiv;
    private Double pml;
    private Double exposure;
    private Double geocodingResolution;
    private String geocoding;
    private Double pmlUtilisation;
    private Double dfUtilisation;
    @Indexed
    private String firstExpiry;

    @Field("riskData")
    private RiskDataDTO riskData; // War, Terrorism, Civil Unrest, Combined

    private Map<String, Double> locationToDistanceMap;
    private int locationCount;
    private Instant updatedOn;

    private Boolean needsGeocoding = true;
}


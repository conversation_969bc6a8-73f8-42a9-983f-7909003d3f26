package com.concirrus.blast_zone_creator.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.geo.GeoJsonPoint;
import org.springframework.data.mongodb.core.index.GeoSpatialIndexType;
import org.springframework.data.mongodb.core.index.GeoSpatialIndexed;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Document(collection = "locations")
public class Location {

    @Id
    private String id;
    private String state;               // {state of submission QUOTED,IN_REVIEW,BOUND}
    private String locationName;
    private String street;
    private String city;
    private Double latitude;
    private Double longitude;
    @Indexed
    private String submissionId;
    private String geocodingGrade;
    private Double geocodingResolution;
    private String locationCurrency;
    private String postalCode;
    @GeoSpatialIndexed(type = GeoSpatialIndexType.GEO_2DSPHERE)
    private GeoJsonPoint geometry;

    private Double biValue12Months;

    private Double biValue12MonthsOriginalCcy;

    private Double buildingValue;

    private Double buildingValueOriginalCcy;

    private Double contentsValue;

    private Double contentsValueOriginalCcy;

    private double tiv;
}


package com.concirrus.blast_zone_creator.model.workflow;


import com.concirrus.blast_zone_creator.dto.enums.ProcessingStatus;
import lombok.*;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
@Document("blast_zone_processing_status")
@CompoundIndex(name = "taskId_status_idx", def = "{'taskId': 1, 'status': 1}")
public class BlastZoneProcessingStatus {
    private String taskId;
    private Long blastZoneId;
    private ProcessingStatus status;
    private Instant processedAt;
    private Instant createdAt;
}

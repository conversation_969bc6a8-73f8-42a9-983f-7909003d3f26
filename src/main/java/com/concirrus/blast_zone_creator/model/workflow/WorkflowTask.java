package com.concirrus.blast_zone_creator.model.workflow;

import com.concirrus.blast_zone_creator.dto.enums.ProcessingStatus;
import com.concirrus.blast_zone_creator.dto.enums.UpdateType;
import lombok.*;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
@Document("workflow_task")
public class WorkflowTask {
    @Id
    private String taskId;

    @Indexed
    private String jobId;
    private String taskEntity;
    private String taskEntityId;
    private UpdateType updateType;

    private ProcessingStatus status;
    private long blastZonesToProcess;

    private Instant createdOn;
    private Instant updatedOn;
}

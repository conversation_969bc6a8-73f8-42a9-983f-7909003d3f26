package com.concirrus.blast_zone_creator.model.miAnalysis;

import com.concirrus.blast_zone_creator.dto.enums.ProcessingStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Document("miAnalysis_tasks")
public class MiAnalysisTask {

    @Id
    private String taskId;
    private String jobId;
    private String peril;
    private ProcessingStatus status;
    private double deductible;
    private double excess;
    private double line;
    private double limit;


    private Instant createdAt;
    private Instant updatedAt;
}

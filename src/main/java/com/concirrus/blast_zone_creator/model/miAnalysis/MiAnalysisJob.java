package com.concirrus.blast_zone_creator.model.miAnalysis;

import com.concirrus.blast_zone_creator.dto.enums.ProcessingStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Document("miAnalysis_jobs")
public class MiAnalysisJob {
    @Id
    private String jobId;

    private ProcessingStatus status;

    private String submissionId;

    private String quoteId;

    private Instant createdAt;

    private Instant updatedAt;
}

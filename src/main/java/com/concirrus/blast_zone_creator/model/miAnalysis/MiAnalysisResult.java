package com.concirrus.blast_zone_creator.model.miAnalysis;


import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@Document("analysis_result")
public class MiAnalysisResult {
    private String taskId;
    private String blastZoneId;
    private String blastZoneName;
    private Double currentExposure;
    private Double currentExposureUtilisation;
    private Double currentPmlContribution;
    private Double currentPmlUtilisation;
    private Double updatedExposure;
    private Double updatedExposureUtilisation=0.0;
    private Double accountContribution;
    private Double updatedPmlExposure;
    private Double updatedPmlUtilisation;
    private Double updatedGeocodingResolution;
    private String updatedGeocodingGrade;
}

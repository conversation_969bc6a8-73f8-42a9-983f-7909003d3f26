package com.concirrus.blast_zone_creator.controller;


import com.concirrus.blast_zone_creator.config.TenantContextHolder;
import com.concirrus.blast_zone_creator.service.BlastZoneService;
import com.concirrus.blast_zone_creator.service.RecomputationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import static com.concirrus.blast_zone_creator.constants.Constants.CLIENT_ID;

/**
 * BlastZone Controller with Multitenant Support
 *
 * All endpoints require 'client-id' header for tenant identification.
 * The TenantInterceptor automatically:
 * 1. Extracts client-id from request headers
 * 2. Sets tenant context using TenantContextHolder
 * 3. Routes database operations to tenant-specific database (aggregation_db_{tenantAlias})
 * 4. Cleans up context after request completion
 *
 * Example usage:
 * curl -H "client-id: your-client-id" -X POST /api/blast-zone/recompute
 */
@RestController
@RequestMapping("/api/blast-zone")
@RequiredArgsConstructor
@Slf4j
public class BlastZoneController {

    private final BlastZoneService blastZoneService;
    private final RecomputationService recomputationService;

    /**
     * Recompute blast zone attributes for the current tenant
     *
     * @param clientId Client ID header for tenant identification (automatically handled by interceptor)
     * @return Status message
     */
    @PostMapping("/recompute")
    public String recomputeBlastZoneAttributes(
            @RequestHeader(value = CLIENT_ID) String clientId) {

        String tenantId = TenantContextHolder.getTenantId();
        String tenantAlias = TenantContextHolder.getTenantAlias();

        log.info("Starting blast zone recomputation for tenant: {} (alias: {})", tenantId, tenantAlias);

        recomputationService.recomputeBlastZoneAttributes();

        log.info("Blast zone recomputation started successfully for tenant: {}", tenantId);
        return "Recomputation started for tenant: " + tenantAlias;
    }

    /**
     * Delete blast zone location mapping for the current tenant
     *
     * @param clientId Client ID header for tenant identification (automatically handled by interceptor)
     * @param locationId Optional location ID to delete specific mapping
     * @param submissionId Submission ID for mapping identification
     * @return Status message
     */
    @DeleteMapping("/delete-mapping")
    public String deleteBlastZoneLocationMapping(
            @RequestHeader(value = CLIENT_ID) String clientId,
            @RequestParam(value = "locationId", required = false) String locationId,
            @RequestParam(value = "submissionId") String submissionId) {

        String tenantId = TenantContextHolder.getTenantId();
        String tenantAlias = TenantContextHolder.getTenantAlias();

        log.info("Deleting blast zone location mapping for tenant: {} (alias: {}), submissionId: {}, locationId: {}",
                tenantId, tenantAlias, submissionId, locationId);

        if (locationId != null) {
            blastZoneService.deleteBlastZoneLocationMapping(locationId, submissionId);
            log.info("Deleted specific location mapping for tenant: {}, locationId: {}", tenantId, locationId);
        } else {
            blastZoneService.deleteBlastZoneLocationMappingBySubmissionId(submissionId);
            log.info("Deleted all location mappings for tenant: {}, submissionId: {}", tenantId, submissionId);
        }

        return "Deleted mapping for tenant: " + tenantAlias;
    }
}

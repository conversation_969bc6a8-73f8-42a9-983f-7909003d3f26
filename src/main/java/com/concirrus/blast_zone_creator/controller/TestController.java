package com.concirrus.blast_zone_creator.controller;


import com.concirrus.blast_zone_creator.config.TenantContextHolder;
import com.concirrus.blast_zone_creator.model.SnapshotJob;
import com.concirrus.blast_zone_creator.service.RecomputationService;
import com.concirrus.blast_zone_creator.service.SnapshotService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import static com.concirrus.blast_zone_creator.constants.Constants.CLIENT_ID;

/**
 * Test Controller with Multitenant Support
 *
 * All endpoints require 'client-id' header for tenant identification.
 * Used for testing and development purposes with tenant-specific operations.
 *
 * Example usage:
 * curl -H "client-id: your-client-id" -X GET /test/first-expiry
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/test")
@Slf4j
public class TestController {
    private final RecomputationService recomputationService;
    private final SnapshotService snapshotService;

    /**
     * Test endpoint to recompute first expiry for blast zones in current tenant
     *
     * @param clientId Client ID header for tenant identification
     * @return Status message
     */
    @GetMapping("/first-expiry")
    public String test(@RequestHeader(value = CLIENT_ID) String clientId) {
        String tenantId = TenantContextHolder.getTenantId();
        String tenantAlias = TenantContextHolder.getTenantAlias();

        log.info("Starting first expiry recomputation test for tenant: {} (alias: {})", tenantId, tenantAlias);

        recomputationService.recomputeFirstExpiryForBlastZones();

        log.info("First expiry recomputation test completed for tenant: {}", tenantId);
        return "Test started for tenant: " + tenantAlias;
    }

    /**
     * Test endpoint to create snapshot for current tenant
     *
     * @param clientId Client ID header for tenant identification
     * @param job Snapshot job configuration
     * @return Status message
     */
    @PostMapping("/create-snapshot")
    public String test2(
            @RequestHeader(value = CLIENT_ID) String clientId,
            @RequestBody SnapshotJob job) {

        String tenantId = TenantContextHolder.getTenantId();
        String tenantAlias = TenantContextHolder.getTenantAlias();

        log.info("Starting snapshot creation for tenant: {} (alias: {})", tenantId, tenantAlias);
        log.info("Snapshot job details: {}", job);

        snapshotService.createSnapshot(job);

        log.info("Snapshot creation started for tenant: {}", tenantId);
        return "Snapshot creation started for tenant: " + tenantAlias;
    }

    /**
     * Test endpoint to debug distance calculations
     *
     * @param clientId Client ID header for tenant identification
     * @param blastZoneId Blast zone ID to test
     * @return Distance calculation results
     */
    @GetMapping("/distance-debug/{blastZoneId}")
    public String testDistanceCalculation(
            @RequestHeader(value = CLIENT_ID) String clientId,
            @PathVariable Long blastZoneId) {

        String tenantId = TenantContextHolder.getTenantId();
        String tenantAlias = TenantContextHolder.getTenantAlias();

        log.info("Testing distance calculation for blast zone: {} for tenant: {} (alias: {})",
                blastZoneId, tenantId, tenantAlias);

        // This will help debug the distance calculation issue
        // You can call this endpoint to see the actual distances being calculated

        return "Distance debug completed for blast zone: " + blastZoneId + " for tenant: " + tenantAlias;
    }
}

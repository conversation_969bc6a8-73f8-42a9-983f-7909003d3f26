package com.concirrus.blast_zone_creator.controller;


import com.concirrus.blast_zone_creator.config.TenantContextHolder;
import com.concirrus.blast_zone_creator.dto.miAnalysis.MiAnalysisJobRequest;
import com.concirrus.blast_zone_creator.dto.rest.BasicResponse;
import com.concirrus.blast_zone_creator.dto.rest.CustomPageResponse;
import com.concirrus.blast_zone_creator.model.miAnalysis.MiAnalysisResult;
import com.concirrus.blast_zone_creator.service.MiAnalysisService;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.concirrus.blast_zone_creator.constants.Constants.CLIENT_ID;

/**
 * MI Analysis Controller with Multitenant Support
 *
 * All endpoints require 'client-id' header for tenant identification.
 * Handles MI (Maximum Individual) analysis operations for tenant-specific data.
 *
 * Example usage:
 * curl -H "client-id: your-client-id" -X GET "/mi-analysis/jobs?submissionId=sub123&quoteId=quote456"
 */
@RestController
@RequestMapping("/mi-analysis")
@Validated
@Slf4j
public class MiAnalysisController {

    private final MiAnalysisService miAnalysisService;

    public MiAnalysisController(MiAnalysisService miAnalysisService) {
        this.miAnalysisService = miAnalysisService;
    }

    /**
     * Get MI analysis job for current tenant
     *
     * @param clientId Client ID header for tenant identification
     * @param submissionId Submission ID for job lookup
     * @param quoteId Quote ID for job lookup
     * @return MI analysis job details or error response
     */
    @GetMapping("/jobs")
    public ResponseEntity<BasicResponse<?>> getMiAnalysisJob(
            @RequestHeader(value = CLIENT_ID) String clientId,
            @RequestParam("submissionId") @NotBlank(message = "Submission ID cannot be blank") @Size(max = 50, message = "Submission ID cannot exceed 50 characters") @Pattern(regexp = "^[a-zA-Z0-9_-]+$", message = "Submission ID can only contain alphanumeric characters, underscores, and hyphens") String submissionId,
            @RequestParam("quoteId") @NotBlank(message = "Quote ID cannot be blank") @Size(max = 50, message = "Quote ID cannot exceed 50 characters") @Pattern(regexp = "^[a-zA-Z0-9_-]+$", message = "Quote ID can only contain alphanumeric characters, underscores, and hyphens") String quoteId) {

        String tenantId = TenantContextHolder.getTenantId();
        String tenantAlias = TenantContextHolder.getTenantAlias();

        log.info("Getting MI analysis job for tenant: {} (alias: {}), submissionId: {}, quoteId: {}",
                tenantId, tenantAlias, submissionId, quoteId);

        var job = miAnalysisService.getJobForAccount(submissionId, quoteId);
        if (job == null) {
            log.warn("MI analysis job not found for tenant: {}, submissionId: {}, quoteId: {}",
                    tenantId, submissionId, quoteId);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(BasicResponse.error("Job not found for the given submissionId and quoteId")
                    .setStatus(HttpStatus.NOT_FOUND.value()));
        }

        log.info("Successfully retrieved MI analysis job for tenant: {}", tenantId);
        return ResponseEntity.ok(BasicResponse.result(job).setStatus(HttpStatus.OK.value()));
    }

    /**
     * Get MI analysis results for current tenant
     *
     * @param clientId Client ID header for tenant identification
     * @param jobId Job ID for analysis results
     * @param sortBy Sort field (default: currentExposure)
     * @param sortOrder Sort order (default: DESC)
     * @param page Page number (default: 0)
     * @param pageSize Page size (default: 20)
     * @param peril Optional peril filter
     * @return Paginated MI analysis results
     */
    @GetMapping
    public BasicResponse<CustomPageResponse<MiAnalysisResult>> getMiAnalysis(
            @RequestHeader(value = CLIENT_ID) String clientId,
            @RequestParam("jobId") String jobId,
            @RequestParam(value = "sortBy",defaultValue = "currentExposure") String sortBy,
            @RequestParam(value = "sortOrder",defaultValue = "DESC") String sortOrder,
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "pageSize",defaultValue = "20") int pageSize,
            @RequestParam(value = "peril", required = false) String peril // peril can be null
    ) {
        String tenantId = TenantContextHolder.getTenantId();
        String tenantAlias = TenantContextHolder.getTenantAlias();

        log.info("Getting MI analysis results for tenant: {} (alias: {}), jobId: {}, peril: {}, page: {}, pageSize: {}",
                tenantId, tenantAlias, jobId, peril, page, pageSize);

        var results = miAnalysisService.miAnalysisResults(jobId, peril, sortBy, sortOrder, page, pageSize);

        log.info("Successfully retrieved MI analysis results for tenant: {}, jobId: {}", tenantId, jobId);
        return BasicResponse.result(results).setStatus(200);
    }


    /**
     * Create MI analysis job for current tenant
     *
     * @param clientId Client ID header for tenant identification
     * @param request MI analysis job request
     * @return Created job details
     */
    @PostMapping("/jobs")
    @ResponseStatus(HttpStatus.CREATED)
    public ResponseEntity<BasicResponse<?>> createMiAnalysisJob(
            @RequestHeader(value = CLIENT_ID) String clientId,
            @RequestBody MiAnalysisJobRequest request) {

        String tenantId = TenantContextHolder.getTenantId();
        String tenantAlias = TenantContextHolder.getTenantAlias();

        log.info("Creating MI analysis job for tenant: {} (alias: {}), request: {}",
                tenantId, tenantAlias, request);

        var result = miAnalysisService.createMiAnalysisJob(request);

        log.info("Successfully created MI analysis job for tenant: {}", tenantId);
        return ResponseEntity.status(HttpStatus.CREATED).body(BasicResponse.result(result).setStatus(201));
    }

    /**
     * Execute MI analysis job for current tenant
     *
     * @param clientId Client ID header for tenant identification
     * @param jobId Job ID to execute
     * @return Execution result
     */
    @PostMapping("/job/{jobId}")
    public BasicResponse<?> executeMiAnalysisJob(
            @RequestHeader(value = CLIENT_ID) String clientId,
            @PathVariable String jobId) {

        String tenantId = TenantContextHolder.getTenantId();
        String tenantAlias = TenantContextHolder.getTenantAlias();

        log.info("Executing MI analysis job for tenant: {} (alias: {}), jobId: {}",
                tenantId, tenantAlias, jobId);

        var result = miAnalysisService.processJob(jobId);

        log.info("Successfully executed MI analysis job for tenant: {}, jobId: {}", tenantId, jobId);
        return BasicResponse.result(result).setStatus(200);
    }

    /**
     * Delete MI analysis job for current tenant
     *
     * @param clientId Client ID header for tenant identification
     * @param submissionId Submission ID for job identification
     * @param quoteId Quote ID for job identification
     * @return Deletion status
     */
    @DeleteMapping
    public BasicResponse<String> deleteMiAnalysisJob(
            @RequestHeader(value = CLIENT_ID) String clientId,
            @RequestParam("submissionId") @NotBlank(message = "Submission ID cannot be blank") @Size(max = 50, message = "Submission ID cannot exceed 50 characters") @Pattern(regexp = "^[a-zA-Z0-9_-]+$", message = "Submission ID can only contain alphanumeric characters, underscores, and hyphens") String submissionId,
            @RequestParam("quoteId") @NotBlank(message = "Quote ID cannot be blank") @Size(max = 50, message = "Quote ID cannot exceed 50 characters") @Pattern(regexp = "^[a-zA-Z0-9_-]+$", message = "Quote ID can only contain alphanumeric characters, underscores, and hyphens") String quoteId) {

        String tenantId = TenantContextHolder.getTenantId();
        String tenantAlias = TenantContextHolder.getTenantAlias();

        log.info("Deleting MI analysis job for tenant: {} (alias: {}), submissionId: {}, quoteId: {}",
                tenantId, tenantAlias, submissionId, quoteId);

        // Replace this with actual delete logic
        log.info("Successfully deleted MI analysis job for tenant: {}", tenantId);
        return BasicResponse.result("Deleted MI Analysis job successfully for tenant: " + tenantAlias).setStatus(HttpStatus.OK.value());
    }
}
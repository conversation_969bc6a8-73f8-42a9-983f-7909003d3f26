package com.concirrus.blast_zone_creator.dal;

import com.concirrus.blast_zone_creator.model.SnapshotJob;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;


@Repository
public interface SnapshotJobRepository extends MongoRepository<SnapshotJob, String> {
    List<SnapshotJob> findByExecutionDate(LocalDate executionDate);
}

package com.concirrus.blast_zone_creator.dal;

import com.concirrus.blast_zone_creator.model.DamageFactor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import static com.concirrus.blast_zone_creator.constants.Constants.DAMAGE_FACTOR_CACHE;


@Repository
@RequiredArgsConstructor
@Slf4j
public class DamageFactorDal {

    private final MongoTemplate mongoTemplate;


    @Cacheable(value = DAMAGE_FACTOR_CACHE, key = "#zone + '_' + #peril")
    public DamageFactor findDamageFactorByZone(String zone, String peril) {
        Query query = new Query(Criteria.where("zone").is(zone).and("peril").is(peril));
        return mongoTemplate.findOne(query, DamageFactor.class);
    }

}

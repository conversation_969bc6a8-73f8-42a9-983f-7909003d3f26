package com.concirrus.blast_zone_creator.dal.blastzone;

import com.concirrus.blast_zone_creator.model.blastzone.BlastZone;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.geo.Distance;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.geo.GeoJsonPoint;
import org.springframework.data.mongodb.core.geo.Sphere;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@RequiredArgsConstructor
@Repository
@Slf4j
public class BlastZoneDAL {

    private final MongoTemplate mongoTemplate;

    public List<BlastZone> getBlastZonesByIds(List<Long> blastZoneIds) {
       Query query = new Query(Criteria.where("id").in(blastZoneIds));
        return mongoTemplate.find(query, BlastZone.class);
    }

    public List<Long> findBlastZoneIdsNear(
           double longitude,double latitude, Distance radius) {
      Query query = new Query(
                Criteria.where("centre")
                        .within(new Sphere(new GeoJsonPoint(longitude, latitude), radius))
        );
        return mongoTemplate.find(query, BlastZone.class)
                .stream()
                .map(BlastZone::getId)
                .toList();
    }

    public Stream<BlastZone> stream(List<Long>blastZoneIds) {
        return mongoTemplate.stream(new Query().addCriteria(Criteria.where("id").in(blastZoneIds)).cursorBatchSize(100), BlastZone.class);
    }

    public List<BlastZone> getBlastZonesNeedingGeocoding(int limit) {
        Query query = new Query()
                .addCriteria(Criteria.where("needsGeocoding").is(true))
                .with(Sort.by(Sort.Direction.DESC, "exposure"))
                .limit(limit);

        return mongoTemplate.find(query, BlastZone.class);
    }

    public Set<Long> findExistingBlastZoneIds(Set<Long> candidateIds) {
        Query query = new Query(Criteria.where("id").in(candidateIds));
        query.fields().include("id");

        return mongoTemplate.find(query, BlastZone.class)
                .stream()
                .map(BlastZone::getId)
                .collect(Collectors.toSet());
    }


}

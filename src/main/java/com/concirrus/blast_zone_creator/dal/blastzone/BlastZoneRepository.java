package com.concirrus.blast_zone_creator.dal.blastzone;


import com.concirrus.blast_zone_creator.model.blastzone.BlastZone;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface BlastZoneRepository extends MongoRepository<BlastZone, Long> {


    @Query(value = "{ 'exposure': { $gt: 0 } }", sort = "{ 'exposure': -1 }")
    Page<BlastZone> findTopBlastZonesByExposureGreaterThanZero(Pageable pageable);

    Page<BlastZone> findByFirstExpiryBefore(String todayDate, Pageable pageable);

}


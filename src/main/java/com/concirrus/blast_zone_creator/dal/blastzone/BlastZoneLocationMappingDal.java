package com.concirrus.blast_zone_creator.dal.blastzone;


import com.concirrus.blast_zone_creator.model.blastzone.BlastZoneLocationMapping;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.result.UpdateResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Repository
@Slf4j
public class BlastZoneLocationMappingDal {
    private final MongoTemplate mongoTemplate;
    public void deleteBlastZoneLocationMappingByLocationAndSubmissionId(String locationId, String submissionId) {
        // Implementation for deleting the mapping by blast zone ID
        Query query = new Query();
        query.addCriteria(Criteria.where("locationId").is(locationId)
                .and("submissionId").is(submissionId));
        mongoTemplate.remove(query, "blast_zone_location_mapping");
    }

    public void deleteBlastZoneLocationMappingBySubmissionId( String submissionId) {
        // Implementation for deleting the mapping by blast zone ID
        Query query = new Query();
        query.addCriteria(
                Criteria.where("submissionId").is(submissionId));
        mongoTemplate.remove(query, "blast_zone_location_mapping");
    }
    public List<Long> findBlastZoneIdsBySubmissionId(String submissionId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("submissionId").is(submissionId));
        query.fields().include("blastZoneId").exclude("_id");
        List<Document> documents = mongoTemplate.find(query, Document.class, "blast_zone_location_mapping");
        return documents.stream()
                .map(doc -> doc.getLong("blastZoneId"))
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());

    }

    public List<Long> findBlastZoneIdsByLocationId(String locationId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("locationId").is(locationId));
        query.fields().include("blastZoneId").exclude("_id");
        List<Document> documents = mongoTemplate.find(query, Document.class, "blast_zone_location_mapping");
        return documents.stream()
                .map(doc -> doc.getLong("blastZoneId"))
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());

    }


    public void updateStateForLocationId(String locationId, String state) {
        Query query = new Query();
        query.addCriteria(Criteria.where("locationId").is(locationId));
        Update update = new Update().set("state", state);
        mongoTemplate.updateMulti(query, update, "blast_zone_location_mapping");
    }


    public void upsertAllBlastZoneLocationMappings(List<BlastZoneLocationMapping> mappings) {
        if (mappings == null || mappings.isEmpty()) return;

        try {
            BulkOperations bulkOps = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, BlastZoneLocationMapping.class);

            for (BlastZoneLocationMapping mapping : mappings) {
                if (mapping.getId() != null && !mapping.getId().isEmpty()) {
                    // Update existing document by ID
                    Query query = new Query(Criteria.where("_id").is(mapping.getId()));

                    Document doc = new Document();
                    mongoTemplate.getConverter().write(mapping, doc);
                    doc.remove("_id"); // Remove _id to avoid update conflicts

                    // Use $set operator for proper update syntax
                    Update update = new Update();
                    doc.forEach(update::set);

                    bulkOps.upsert(query, update);
                } else {
                    // Insert new document - let MongoDB generate the ID
                    bulkOps.insert(mapping);
                }
            }

            BulkWriteResult result = bulkOps.execute();
            log.info("Batch upserted {} blast zone location mappings (inserted: {}, modified: {}, upserted: {})",
                    mappings.size(), result.getInsertedCount(), result.getModifiedCount(), result.getUpserts().size());

        } catch (Exception e) {
            log.error("Bulk upsert failed, falling back to individual operations for {} mappings: {}",
                    mappings.size(), e.getMessage());

            // Fallback: Process each mapping individually
            int successCount = 0;
            int failureCount = 0;

            for (BlastZoneLocationMapping mapping : mappings) {
                try {
                    upsertSingleMapping(mapping);
                    successCount++;
                } catch (Exception singleError) {
                    failureCount++;
                    log.error("Failed to upsert single mapping with ID {}: {}",
                            mapping.getId(), singleError.getMessage());
                }
            }

            log.info("Fallback completed - Success: {}, Failures: {} out of {} total mappings",
                    successCount, failureCount, mappings.size());

            if (failureCount > 0) {
                log.warn("Some mappings failed to upsert even with fallback. Check individual error logs above.");
            }
        }
    }

    /**
     * Fallback method to upsert a single mapping individually
     */
    private void upsertSingleMapping(BlastZoneLocationMapping mapping) {
        if (mapping.getId() != null && !mapping.getId().isEmpty()) {
            // Update existing document by ID
            Query query = new Query(Criteria.where("_id").is(mapping.getId()));

            Document doc = new Document();
            mongoTemplate.getConverter().write(mapping, doc);
            doc.remove("_id");

            // Use $set operator for proper update syntax
            Update update = new Update();
            doc.forEach(update::set);

            UpdateResult result = mongoTemplate.upsert(query, update, BlastZoneLocationMapping.class);
            log.debug("Single upsert for ID {}: matched={}, modified={}",
                    mapping.getId(), result.getMatchedCount(), result.getModifiedCount());
        } else {
            // Insert new document
            BlastZoneLocationMapping saved = mongoTemplate.save(mapping);
            log.debug("Single insert completed with generated ID: {}", saved.getId());
        }
    }

}

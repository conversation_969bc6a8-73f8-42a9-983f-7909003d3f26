package com.concirrus.blast_zone_creator.dal.blastzone;

import com.concirrus.blast_zone_creator.dto.SubmissionIdOnly;
import com.concirrus.blast_zone_creator.model.blastzone.BlastZoneLocationMapping;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface BlastZoneLocationMappingRepository extends MongoRepository<BlastZoneLocationMapping, String> {


    @Query(
            value = "{ 'blastZoneId': ?0, 'state': ?1 }",
            fields = "{ 'submissionId': 1, '_id': 0 }"
    )
    List<SubmissionIdOnly> findSubmissionIdsByBlastZoneIdAndState(Long blastZoneId, String state);


}

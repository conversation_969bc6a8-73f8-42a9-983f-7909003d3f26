package com.concirrus.blast_zone_creator.dal;

import com.concirrus.blast_zone_creator.dto.LocationProjection;
import com.concirrus.blast_zone_creator.model.Location;
import com.concirrus.blast_zone_creator.model.blastzone.BlastZone;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.geo.Distance;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.geo.GeoJsonPoint;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.NearQuery;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Stream;


@Repository
@RequiredArgsConstructor
@Slf4j
public class LocationDAL {
    private final MongoTemplate mongoTemplate;
    public Stream<Location>stream(String submissionId){
            return mongoTemplate.stream(new Query().cursorBatchSize(100), Location.class);
    }

    public Location getLocationById(String id){
        return mongoTemplate.findOne(new Query().addCriteria(Criteria.where("id").is(id)), Location.class);
    }

    public List<Location> findLocationsBySubmissionId(String submissionId) {
        Query query = new Query(Criteria.where("submissionId").is(submissionId));
        return mongoTemplate.find(query, Location.class);
    }


    /**
     * Find locations within blast zone with accurate distance calculation in meters
     *
     * IMPORTANT: MongoDB $geoNear with spherical:true returns distances in RADIANS
     * We need to convert properly to get distances in meters.
     *
     * @param blastZone The blast zone center point
     * @param radius The search radius (typically 0.250 km from Constants.BLAST_ZONE_RADIUS)
     * @return List of locations with accurate distances in meters
     */
    public List<LocationProjection> findWithinBlastZoneWithDistance(BlastZone blastZone, Distance radius) {
        GeoJsonPoint center = blastZone.getCentre();

        // Get radius in meters
        double radiusInMeters = radius.getNormalizedValue();

        // For MongoDB geoNear with spherical=true, maxDistance should be in meters when using meters as unit
        NearQuery nearQuery = NearQuery
                .near(center)
                .maxDistance(radiusInMeters) // Use meters directly, not radians
                .spherical(true)
                .distanceMultiplier(6378137.0); // No conversion needed when using meters

        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.geoNear(nearQuery, "distance"),
                Aggregation.project("id", "geometry", "tiv", "state", "buildingValue",
                                "biValue12Months", "contentsValue", "submissionId", "geocodingResolution")
                        .and("distance").as("distance")
        );

        AggregationResults<LocationProjection> results = mongoTemplate.aggregate(
                aggregation,
                "locations", // collection name
                LocationProjection.class
        );

        List<LocationProjection> locations = results.getMappedResults();

        // Log for debugging - remove in production
        if (!locations.isEmpty()) {
            LocationProjection first = locations.getFirst();
            log.debug("Sample distance calculation - Location: {}, Distance: {} meters",
                    first.getId(), first.getDistance());
        }

        return locations;
    }

}

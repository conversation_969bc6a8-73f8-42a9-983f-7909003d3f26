//package com.concirrus.blast_zone_processor.dal.account;
//
//import com.concirrus.blast_zone_processor.model.Account;
//import lombok.RequiredArgsConstructor;
//import org.bson.Document;
//import org.springframework.data.mongodb.core.MongoTemplate;
//import org.springframework.data.mongodb.core.aggregation.*;
//import org.springframework.data.mongodb.core.query.Criteria;
//import org.springframework.stereotype.Repository;
//import org.springframework.util.StringUtils;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.stream.Collectors;
//
//@RequiredArgsConstructor
//@Repository
//public class AccountDAL {
//
//    private final MongoTemplate mongoTemplate;
//    public List<Account> listAccountsSortedFlexible(
//            List<String> submissionIds,String quoteId,Long blastZoneId
//    ) {
//        Criteria accountCriteria = new Criteria();
//
//        accountCriteria.and("submissionId").in(submissionIds);
//        if (StringUtils.hasText(quoteId)){
//            accountCriteria.and("quoteId").is(quoteId);
//        }
//
//        MatchOperation matchAccount = Aggregation.match(accountCriteria);
//
//        LookupOperation lookup = LookupOperation.newLookup()
//                .from("blast_zone_location_mapping")
//                .localField("submissionId")
//                .foreignField("submissionId")
//                .as("mappings");
//
//        // Unwind mappings to access and filter
//        UnwindOperation unwind = Aggregation.unwind("mappings");
//
//        // Filter blast zone + optional filters
//        List<Criteria> mappingFilters = new ArrayList<>();
//        mappingFilters.add(Criteria.where("mappings.blastZoneId").is(blastZoneId));
//
//        MatchOperation matchMappings = Aggregation.match(new Criteria().andOperator(mappingFilters.toArray(new Criteria[0])));
//
//        // Group by _id and collect total PML + TIV
//        GroupOperation group = Aggregation.group("_id")
//                .first("accountName").as("accountName")
//                .first("submissionId").as("submissionId")
//                .first("binder").as("binder")
//                .first("inceptionDate").as("inceptionDate")
//                .first("expiryDate").as("expiryDate")
//                .first("premium").as("premium")
//                .first("line").as("line")
//                .first("limit").as("limit")
//                .first("deductible").as("deductible")
//                .first("excess").as("excess")
//                .first("policyReference").as("policyReference")
//                .sum("mappings.blastZonePml").as("pml")
//                .sum("mappings.blastZoneTiv").as("tiv");
//
//
//
//        // Pagination
//
//
//        Aggregation aggregation = Aggregation.newAggregation(
//                matchAccount,
//                lookup,
//                unwind,
//                matchMappings,
//                group
//        );
//
//        AggregationResults<Document> results = mongoTemplate.aggregate(aggregation, "account", Document.class);
//        List<Document> docs = results.getMappedResults();
//
//        // Count total distinct accounts in blast zone
//        return docs.stream().map(doc -> {
//            Account acc = new Account();
//            acc.setId(doc.getObjectId("_id").toString());
//            acc.setPolicyReference(doc.getString("policyReference"));
//            acc.setLimit(doc.getDouble("limit"));
//            acc.setDeductible(doc.getDouble("deductible"));
//            acc.setExcess(doc.getDouble("excess"));
//            acc.setLine(doc.getDouble("line"));
//            acc.setAccountName(doc.getString("accountName"));
//            acc.setSubmissionId(doc.getString("submissionId"));
//            acc.setBinder(doc.getString("binder"));
//            acc.setInceptionDate(doc.getString("inceptionDate"));
//            acc.setExpiryDate(doc.getString("expiryDate"));
//            acc.setPremium(doc.getDouble("premium"));
//            acc.setPml(doc.getDouble("pml"));
//            acc.setTiv(doc.getDouble("tiv"));
//            return acc;
//        }).collect(Collectors.toList());
//    }
//}

package com.concirrus.blast_zone_creator.dal.account;

import com.concirrus.blast_zone_creator.dto.ExpiryDateOnly;
import com.concirrus.blast_zone_creator.model.Account;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface AccountRepository extends MongoRepository<Account, String> , AccountCustomRepository{

    @Query(value = "{ 'submissionId': { $in: ?0 } }",
            fields = "{ 'expiryDate': 1, 'submissionId': 1 }")
    List<ExpiryDateOnly> findExpiryDatesBySubmissionIds(List<String> submissionIds);
}

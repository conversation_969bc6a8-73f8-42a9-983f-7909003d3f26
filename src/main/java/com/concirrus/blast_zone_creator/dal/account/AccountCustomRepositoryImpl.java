package com.concirrus.blast_zone_creator.dal.account;

import com.concirrus.blast_zone_creator.model.Account;
import lombok.RequiredArgsConstructor;
import org.bson.Document;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor
public class AccountCustomRepositoryImpl implements AccountCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public List<Account> listAccountsSortedFlexible(List<String> submissionIds, String quoteId, Long blastZoneId) {
        Criteria accountCriteria = Criteria.where("submissionId").in(submissionIds);
        if (StringUtils.hasText(quoteId)) {
            accountCriteria.and("quoteId").is(quoteId);
        }

        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(accountCriteria),
                Aggregation.lookup("blast_zone_location_mapping", "submissionId", "submissionId", "mappings"),
                Aggregation.unwind("mappings"),
                Aggregation.match(Criteria.where("mappings.blastZoneId").is(blastZoneId)),
                Aggregation.group("_id")
                        .first("accountName").as("accountName")
                        .first("submissionId").as("submissionId")
                        .first("binder").as("binder")
                        .first("inceptionDate").as("inceptionDate")
                        .first("expiryDate").as("expiryDate")
                        .first("premium").as("premium")
                        .first("line").as("line")
                        .first("limit").as("limit")
                        .first("deductible").as("deductible")
                        .first("excess").as("excess")
                        .first("policyReference").as("policyReference")
                        .sum("mappings.blastZonePml").as("pml")
                        .sum("mappings.blastZoneTiv").as("tiv")
        );

        AggregationResults<Document> results = mongoTemplate.aggregate(aggregation, "account", Document.class);

        return results.getMappedResults().stream().map(doc -> {
            Account acc = new Account();
            acc.setId(doc.getObjectId("_id").toString());
            acc.setPolicyReference(doc.getString("policyReference"));
            acc.setLimit(doc.getDouble("limit"));
            acc.setDeductible(doc.getDouble("deductible"));
            acc.setExcess(doc.getDouble("excess"));
            acc.setLine(doc.getDouble("line"));
            acc.setAccountName(doc.getString("accountName"));
            acc.setSubmissionId(doc.getString("submissionId"));
            acc.setBinder(doc.getString("binder"));
            acc.setInceptionDate(doc.getString("inceptionDate"));
            acc.setExpiryDate(doc.getString("expiryDate"));
            acc.setPremium(doc.getDouble("premium"));
            acc.setPml(doc.getDouble("pml"));
            acc.setTiv(doc.getDouble("tiv"));
            return acc;
        }).collect(Collectors.toList());
    }
}
package com.concirrus.blast_zone_creator.dal.miAnalysis;

import com.concirrus.blast_zone_creator.model.miAnalysis.MiAnalysisJob;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.Optional;

@Repository
public interface MiAnalysisJobRepository  extends MongoRepository<MiAnalysisJob ,String> {

    Optional<MiAnalysisJob> findFirstBySubmissionIdAndQuoteIdOrderByCreatedAtDesc(String submissionId, String quoteId);

    Optional<MiAnalysisJob> findTopBySubmissionIdAndCreatedAtBeforeOrderByCreatedAtDesc(String submissionId, Instant createdAt);
    Optional<MiAnalysisJob> findTopBySubmissionIdAndQuoteIdAndCreatedAtBeforeOrderByCreatedAtDesc(String submissionId, String quoteId, Instant createdAt);

}

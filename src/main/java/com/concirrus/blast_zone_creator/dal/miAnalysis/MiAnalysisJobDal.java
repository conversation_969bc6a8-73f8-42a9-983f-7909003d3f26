package com.concirrus.blast_zone_creator.dal.miAnalysis;


import com.concirrus.blast_zone_creator.model.miAnalysis.MiAnalysisJob;
import lombok.RequiredArgsConstructor;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

@RequiredArgsConstructor
@Repository
public class MiAnalysisJobDal {

    private final MongoTemplate mongoTemplate;


    public MiAnalysisJob getMiAnalysisJobById(String jobId) {
        return mongoTemplate.findById(jobId, MiAnalysisJob.class);
    }

    public MiAnalysisJob getMiAnalysisJobBySubmissionAndJobId(String submissionId, String quoteId) {
        Query  query= new Query();
        query.addCriteria(Criteria.where("submissionId").is(submissionId)
                .and("quoteId").is(quoteId));
        return mongoTemplate.findOne(query, MiAnalysisJob.class);
    }
}

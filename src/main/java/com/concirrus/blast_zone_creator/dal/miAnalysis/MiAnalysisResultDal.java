package com.concirrus.blast_zone_creator.dal.miAnalysis;


import com.concirrus.blast_zone_creator.model.miAnalysis.MiAnalysisResult;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@RequiredArgsConstructor
public class MiAnalysisResultDal {
    private final MongoTemplate mongoTemplate;


    public List<MiAnalysisResult> findAnalysisResults(String taskId, String sortBy, String sortOrder, int page, int size) {
        // Default sort by currentExposure if null or empty
        String sortField = (sortBy == null || sortBy.isBlank()) ? "currentExposure" : sortBy;

        // Determine sort direction
        Sort.Direction direction = "desc".equalsIgnoreCase(sortOrder) ? Sort.Direction.DESC : Sort.Direction.ASC;

        // Build query with sorting and pagination
        Query query = new Query()
                .addCriteria(Criteria.where("taskId").is(taskId))
                .with(Sort.by(direction, sortField))
                .skip((long) page * size)
                .limit(size);

        // Run the query
        return mongoTemplate.find(query, MiAnalysisResult.class);
    }

    public long countAnalysisResults(String taskId) {
        Query query = new Query(Criteria.where("taskId").is(taskId));
        return mongoTemplate.count(query, MiAnalysisResult.class);
    }
}

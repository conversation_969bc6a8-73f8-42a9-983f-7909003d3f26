package com.concirrus.blast_zone_creator.dal.miAnalysis;

import com.concirrus.blast_zone_creator.dto.enums.ProcessingStatus;
import com.concirrus.blast_zone_creator.model.miAnalysis.MiAnalysisTask;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface MiAnalysisTaskRepository extends MongoRepository<MiAnalysisTask, String> {

    List<MiAnalysisTask> findByJobId(String jobId);

    List<MiAnalysisTask> findByJobIdAndStatus(String jobId, ProcessingStatus status);
}

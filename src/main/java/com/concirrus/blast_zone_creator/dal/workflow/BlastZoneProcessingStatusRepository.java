package com.concirrus.blast_zone_creator.dal.workflow;

import com.concirrus.blast_zone_creator.model.workflow.BlastZoneProcessingStatus;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;


@Repository
public interface BlastZoneProcessingStatusRepository extends MongoRepository<BlastZoneProcessingStatus , String> {

    BlastZoneProcessingStatus findByTaskId(String taskId);

    BlastZoneProcessingStatus findByBlastZoneId(Long blastZoneId);

}

package com.concirrus.blast_zone_creator.dal.workflow;


import com.concirrus.blast_zone_creator.dto.enums.ProcessingStatus;
import com.concirrus.blast_zone_creator.model.workflow.BlastZoneProcessingStatus;
import com.mongodb.bulk.BulkWriteResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;

@RequiredArgsConstructor
@Repository
@Slf4j
public class BlastZoneProcessingDal {
    private final MongoTemplate mongoTemplate;
    private static final int BULK_UPDATE_BATCH_SIZE = 100;


    public void updateStatusByBlastZoneIdAndTaskId(Long blastZoneId, String taskId, ProcessingStatus status) {
        Query query = new Query(Criteria.where("blastZoneId").is(blastZoneId)
                .and("taskId").is(taskId));
        Update update = new Update().set("status", status);
        mongoTemplate.updateFirst(query, update, BlastZoneProcessingStatus.class);
    }

    public boolean isInProgress(String taskId) {
        Query query = new Query(Criteria.where("taskId").is(taskId)
                .and("status").is(ProcessingStatus.IN_PROGRESS));
        return mongoTemplate.exists(query, BlastZoneProcessingStatus.class);
    }

    public void batchUpdateStatusWithBulkOps(List<Long> blastZoneIds, String taskId, ProcessingStatus status) {
        if (blastZoneIds == null || blastZoneIds.isEmpty()) {
            return;
        }

        int totalUpdated = 0;
        int totalCount = blastZoneIds.size();
        
        // Process in batches to avoid timeout
        for (int i = 0; i < totalCount; i += BULK_UPDATE_BATCH_SIZE) {
            int endIndex = Math.min(i + BULK_UPDATE_BATCH_SIZE, totalCount);
            List<Long> batch = blastZoneIds.subList(i, endIndex);
            
            BulkOperations bulkOps = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, BlastZoneProcessingStatus.class);

            for (Long blastZoneId : batch) {
                Query query = new Query(Criteria.where("blastZoneId").is(blastZoneId)
                        .and("taskId").is(taskId));

                Update update = new Update()
                        .set("status", status)
                        .set("processedAt", Instant.now());

                bulkOps.updateOne(query, update);
            }

            BulkWriteResult result = bulkOps.execute();
            totalUpdated += result.getModifiedCount();
            
            log.info("Bulk updated batch {}/{}: {} records to status {} for task {}",
                    (i / BULK_UPDATE_BATCH_SIZE) + 1,
                    (totalCount + BULK_UPDATE_BATCH_SIZE - 1) / BULK_UPDATE_BATCH_SIZE,
                    result.getModifiedCount(), status, taskId);
        }

        log.info("Total bulk updated {} out of {} records to status {} for task {}",
                totalUpdated, totalCount, status, taskId);
    }
}

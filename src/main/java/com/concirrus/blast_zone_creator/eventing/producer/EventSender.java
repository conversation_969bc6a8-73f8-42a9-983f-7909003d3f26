package com.concirrus.blast_zone_creator.eventing.producer;

import com.concirrus.blast_zone_creator.config.TenantContextHolder;
import com.concirrus.blast_zone_creator.dal.workflow.TaskRepository;
import com.concirrus.blast_zone_creator.dto.enums.ProcessingStatus;
import com.concirrus.blast_zone_creator.dto.workflow.BlastZoneEventDTO;
import com.concirrus.blast_zone_creator.dto.workflow.TaskCallBack;
import com.concirrus.blast_zone_creator.model.workflow.WorkflowTask;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.cloud.ServiceOptions;
import com.google.cloud.pubsub.v1.Publisher;
import com.google.protobuf.ByteString;
import com.google.pubsub.v1.PubsubMessage;
import com.google.pubsub.v1.TopicName;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

@Slf4j
@Component
public class EventSender {


    private final String gcpProjectId;
    private final String internalQueue;
    private final String callbackQueue ;
    private final TaskRepository taskRepository;
    private final ObjectMapper objectMapper;

    public EventSender( @Value("${cloud.messaging.topic.internal-queue}") String internalQueue,@Value("${cloud.messaging.topic.task-callback}") String callbackQueue, TaskRepository taskRepository, ObjectMapper objectMapper) {
        this.gcpProjectId = ServiceOptions.getDefaultProjectId();
        this.internalQueue = internalQueue;
        this.callbackQueue = callbackQueue;
        this.taskRepository = taskRepository;
        this.objectMapper = objectMapper;
    }



    public void publishMessage(String destinationChannel, String message) {
        log.info("Sending the message: {}", message);

        TopicName topicName = TopicName.of(gcpProjectId, destinationChannel);
        Publisher publisher = null;
        try {
            // Create a publisher instance with default settings bound to the topic
            publisher = Publisher.newBuilder(topicName).build();

            PubsubMessage pubsubMessage = PubsubMessage.newBuilder()
                    .setData(ByteString.copyFromUtf8(message))
                    .build();

            // Once published, returns a server-assigned message id (unique within the topic)
            String messageId = publisher.publish(pubsubMessage).get(1, TimeUnit.MINUTES);
            log.info("Published message ID: {}", messageId);
        } catch (IOException | ExecutionException | InterruptedException | TimeoutException e) {
            log.error("Error pushing message to topic {}", destinationChannel, e);
            throw new RuntimeException(e);
        } finally {
            if (Objects.nonNull(publisher)) {
                // When finished with the publisher, shutdown to free up resources.
                publisher.shutdown();
            }
        }
        log.info("Successfully published to {} queue", destinationChannel);
    }

    public void sendBlastZoneProcessingMessage(BlastZoneEventDTO blastZoneEvent){
        try {
            String message = objectMapper.writeValueAsString(blastZoneEvent);
            publishMessage(internalQueue,message);
            log.info("Successfully sent {} blastZones for processing",blastZoneEvent.getBlastZoneIds().size());
        } catch (JsonProcessingException e) {
            log.info("Unable to parse blast zone to String message");
        }

    }

    /**
     * Sends a callback message for a specific task.
     * This method is asynchronous and will not block the caller.
     *
     * @param taskId The ID of the task to send the callback for.
     */

    @Async
    public void sendTaskCallBack(String taskId) {
        WorkflowTask task = taskRepository.findById(taskId)
                .orElseThrow(() -> new RuntimeException("Task not found with ID: " + taskId));

        if (TenantContextHolder.getTenantId() == null) {
            log.error("Tenant context is null, cannot send task callback");
            return;
        }

        TaskCallBack callBack = TaskCallBack.builder()
                .taskId(task.getTaskId())
                .locationId(task.getTaskEntityId())
                .jobId(task.getJobId())
                .status(ProcessingStatus.COMPLETED.name())
                .clientId(TenantContextHolder.getTenantId())
                .build();

        try {
            String message = objectMapper.writeValueAsString(callBack);
            log.info("Sending task callback for taskId: {}", taskId);
            publishMessage(callbackQueue, message);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }


    }
}

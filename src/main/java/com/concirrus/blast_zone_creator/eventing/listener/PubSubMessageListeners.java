package com.concirrus.blast_zone_creator.eventing.listener;

import com.concirrus.blast_zone_creator.config.TenantContextHolder;
import com.concirrus.blast_zone_creator.dto.workflow.BlastZoneEventDTO;
import com.concirrus.blast_zone_creator.dto.workflow.LocationEventDTO;
import com.concirrus.blast_zone_creator.service.TenantService;
import com.concirrus.blast_zone_creator.service.WorkflowService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.cloud.spring.pubsub.support.BasicAcknowledgeablePubsubMessage;
import com.google.cloud.spring.pubsub.support.GcpPubSubHeaders;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.time.Instant;

@Slf4j
@Component
@RequiredArgsConstructor
public class PubSubMessageListeners {

    private final TenantService tenantService;
    private final WorkflowService workflowService;
    private final ObjectMapper objectMapper;

    // Listener for incoming location updates
    @ServiceActivator(inputChannel = "locationInputChannel")
    public void handleLocationUpdate(String payload, @Header(GcpPubSubHeaders.ORIGINAL_MESSAGE) BasicAcknowledgeablePubsubMessage message) {
        try {
            LocationEventDTO event = objectMapper.readValue(payload, LocationEventDTO.class);
            String messageId = message.getPubsubMessage().getMessageId();
            Instant startTime = Instant.now();
            setTenantContext(event.getClientId());
            log.info("Received event {} for tenant {}", payload, event.getClientId());
            switch (event.getUpdateType()) {
                case SUBMISSION_REVIEWED, SUBMISSION_BOUND, SUBMISSION_DELETED -> {
                    workflowService.processSubmissionUpdate(event);
                }
                case LOCATION_CREATED, LOCATION_UPDATED, LOCATION_DATA_UPDATED, LOCATION_DELETED -> {
                    workflowService.processLocationUpdate(event);
                }
                case LOCATION_COORDINATES_UPDATED -> {
                    workflowService.processLocationCoordinateUpdate(event);
                }
                default -> {
                    log.info("Received invalid update type: {}", event.getUpdateType());
                }
            }
            Instant endTime = Instant.now();
            message.ack();
            log.info("Location update {} processed in {} s", messageId, (endTime.toEpochMilli() - startTime.toEpochMilli()) / 1000);
        } catch (JsonProcessingException e) {
            log.error("Error occurred while trying to parse locationEvent message ... ignoring message  {}", payload, e);
            message.ack();
        } catch (Exception e) {
            message.ack();
            log.error("Error occurred while trying to process locationEvent message  {}", payload, e);
        }
    }

    private void setTenantContext(String tenantId) {
        TenantContextHolder.setTenantId(tenantId);
        TenantContextHolder.setTenantAlias(tenantService.getTenantAlias(tenantId));
    }
}
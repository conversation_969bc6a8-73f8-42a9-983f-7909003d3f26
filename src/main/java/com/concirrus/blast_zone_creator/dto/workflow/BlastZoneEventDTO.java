package com.concirrus.blast_zone_creator.dto.workflow;

import com.concirrus.blast_zone_creator.dto.enums.Attributes;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BlastZoneEventDTO {
    private List<Long> blastZoneIds;
    private Attributes attributesToRecompute;
    @Builder.Default
    private Boolean reverseGeocodingEnabled = false;
    private String taskId;
    private String clientId;
}

package com.concirrus.blast_zone_creator.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.geo.GeoJsonPoint;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LocationProjection {
    private String id;
    private GeoJsonPoint geometry;
    private Double tiv;     // Use specific type if available
    private String state;
    private String submissionId;
    private Double geocodingResolution;
    private Double distance;

    private Double buildingValue;
    private Double contentsValue;
    private Double biValue12Months;

}

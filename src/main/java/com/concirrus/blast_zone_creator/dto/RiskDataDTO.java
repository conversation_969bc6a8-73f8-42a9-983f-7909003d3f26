package com.concirrus.blast_zone_creator.dto;

import com.concirrus.blast_zone_creator.dto.riskData.RiskData;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RiskDataDTO {
    private Double war;
    private Double terrorism;
    private Double civilUnrest;
    private Double combined;


    public static RiskDataDTO fromRiskData(RiskData riskData) {
        return new RiskDataDTO(
                riskData.getWar(),
                riskData.getTerrorism(),
                riskData.getCivilUnrest(),
                riskData.getAllSecurity()
        );
    }
    public static RiskDataDTO empty() {
        return new RiskDataDTO(0.0, 0.0, 0.0, 0.0);
    }
}
package com.concirrus.blast_zone_creator.dto.riskData;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class RiskData {

    @JsonProperty("War")
    private double War;
    @JsonProperty("CivilUnrest")
    private double CivilUnrest;
    @JsonProperty("AllSecurity")
    private double AllSecurity;
    @JsonProperty("PoliticalViolence")
    private double PoliticalViolence;
    @JsonProperty("Terrorism")
    private double Terrorism;
    @JsonProperty("Crime")
    private double Crime;
}

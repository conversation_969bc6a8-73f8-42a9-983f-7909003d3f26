package com.concirrus.blast_zone_creator.dto.geocoding;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GeocodingResponse {
    
    private Long id;
    private String fullAddress;
    private List<Double> coordinates;
    private String name;
    private String city;
    private String country;
    private String postalCode;
}

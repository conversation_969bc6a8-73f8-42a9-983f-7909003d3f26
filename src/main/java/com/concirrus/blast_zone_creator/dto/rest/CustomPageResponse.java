package com.concirrus.blast_zone_creator.dto.rest;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomPageResponse<T> {
    private long total;
    private boolean hasMore;
    private int page;
    private int size;
    private List<T> data;

    public static <T> CustomPageResponse<T> empty(int page) {
        CustomPageResponse<T> response = new CustomPageResponse<>();
        response.setData(List.of());
        response.setHasMore(false);
        response.setTotal(0);
        response.setPage(page);
        return response;
    }

    public static <T> CustomPageResponse<T> of(List<T> data, long total, int page, boolean hasMore) {
        CustomPageResponse<T> response = new CustomPageResponse<>();
        response.setData(data);
        response.setTotal(total);
        response.setPage(page);
        response.setHasMore(hasMore);
        return response;
    }
}


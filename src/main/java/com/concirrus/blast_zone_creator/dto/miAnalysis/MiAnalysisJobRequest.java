package com.concirrus.blast_zone_creator.dto.miAnalysis;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MiAnalysisJobRequest {

    private String submissionId;
    private String quoteId;
    private Double srcc;
    private Double war;
    @JsonProperty("sAndT")
    private Double sAndT;
    private List<String> perils;
    private Double excess;
    private Double line;
    private Double deductible;
    private Boolean initiateJob = true;


}

package com.concirrus.blast_zone_creator.service;

import com.concirrus.blast_zone_creator.config.TenantContextHolder;
import com.concirrus.blast_zone_creator.dal.DamageFactorDal;
import com.concirrus.blast_zone_creator.dal.LocationDAL;
import com.concirrus.blast_zone_creator.dal.account.AccountRepository;
import com.concirrus.blast_zone_creator.dal.blastzone.BlastZoneLocationMappingDal;
import com.concirrus.blast_zone_creator.dal.blastzone.BlastZoneLocationMappingRepository;
import com.concirrus.blast_zone_creator.dal.blastzone.BlastZoneRepository;
import com.concirrus.blast_zone_creator.dal.workflow.BlastZoneProcessingDal;
import com.concirrus.blast_zone_creator.dal.workflow.TaskRepository;
import com.concirrus.blast_zone_creator.dto.ExpiryDateOnly;
import com.concirrus.blast_zone_creator.dto.LocationProjection;
import com.concirrus.blast_zone_creator.dto.SubmissionIdOnly;
import com.concirrus.blast_zone_creator.dto.enums.Attributes;
import com.concirrus.blast_zone_creator.dto.enums.ProcessingStatus;
import com.concirrus.blast_zone_creator.dto.miAnalysis.AnalysisPML;
import com.concirrus.blast_zone_creator.eventing.producer.EventSender;
import com.concirrus.blast_zone_creator.model.Account;
import com.concirrus.blast_zone_creator.model.DamageFactor;
import com.concirrus.blast_zone_creator.model.Location;
import com.concirrus.blast_zone_creator.model.blastzone.BlastZone;
import com.concirrus.blast_zone_creator.model.blastzone.BlastZoneLocationMapping;
import com.concirrus.blast_zone_creator.model.miAnalysis.MiAnalysisTask;
import com.concirrus.blast_zone_creator.model.workflow.WorkflowTask;
import com.concirrus.blast_zone_creator.utils.DamageFactorUtils;
import com.concirrus.blast_zone_creator.utils.DistanceUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicReference;

import static com.concirrus.blast_zone_creator.config.Constants.*;
import static com.concirrus.blast_zone_creator.utils.AttributeComputationUtils.*;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;

@Service

@Slf4j
public class AttributeComputationService {

    private final BlastZoneRepository blastZoneRepository;
    private final AccountRepository accountRepository;
    private final LocationDAL locationDAL;
    private final DamageFactorDal damageFactorDal;
    private final BlastZoneLocationMappingRepository locationMappingRepository;
    private final BlastZoneLocationMappingDal locationMappingDal;
    private final BlastZoneProcessingDal blastZoneProcessingDal;
    private final TaskRepository taskRepository;
    private final EventSender eventSender;
    private final ExecutorService executorService;


    private static final int LOCATION_CHUNK_SIZE = 1000;
    private static final int MAPPING_BATCH_SIZE = 5000;

    public AttributeComputationService(BlastZoneRepository blastZoneRepository, AccountRepository accountRepository, LocationDAL locationDAL, DamageFactorDal damageFactorDal, BlastZoneLocationMappingRepository locationMappingRepository, BlastZoneLocationMappingDal locationMappingDal, BlastZoneProcessingDal blastZoneProcessingDal, TaskRepository taskRepository, EventSender eventSender, @Qualifier("virtualThreadExecutor") ExecutorService executorService) {
        this.blastZoneRepository = blastZoneRepository;
        this.accountRepository = accountRepository;
        this.locationDAL = locationDAL;
        this.damageFactorDal = damageFactorDal;
        this.locationMappingRepository = locationMappingRepository;
        this.locationMappingDal = locationMappingDal;
        this.blastZoneProcessingDal = blastZoneProcessingDal;
        this.taskRepository = taskRepository;
        this.eventSender = eventSender;
        this.executorService = executorService;
    }


    public void computeAttributesForBlastZones(List<BlastZone> blastZoneList, Attributes attributesToRecompute, String taskId) {

        List<BlastZone> successfulBlastZones = Collections.synchronizedList(new ArrayList<>());
        List<Long> successfulBlastZoneIds = Collections.synchronizedList(new ArrayList<>());
        List<Long> failedBlastZoneIds = Collections.synchronizedList(new ArrayList<>());


        // Capture tenant context before async operations
        String tenantId = TenantContextHolder.getTenantId();
        String tenantAlias = TenantContextHolder.getTenantAlias();

        log.info("Starting Computing {} attributes for blast zones: {} for tenant: {}",
                attributesToRecompute, blastZoneList.size(), tenantId);

        List<CompletableFuture<Void>> futures = blastZoneList.stream()
                .map(blastZone -> CompletableFuture.runAsync(() -> {
                    try {
                        // Set tenant context in async thread
                        if (tenantId != null) {
                            TenantContextHolder.setTenantContext(tenantId, tenantAlias);
                        }

                        switch (attributesToRecompute) {
                            case ALL -> {
                                computePML(blastZone);
                                computeExposure(blastZone);
                            }
                            case PML -> computePML(blastZone);
                            case EXPOSURE -> computeExposure(blastZone);
                        }
                        blastZone.setUpdatedOn(Instant.now());
                        successfulBlastZones.add(blastZone);
                        successfulBlastZoneIds.add(blastZone.getId());

                    } catch (Exception e) {
                        log.info("Error processing blast zone ID {} for task {}: {}", blastZone.getId(), taskId, e.getMessage(), e);
                        failedBlastZoneIds.add(blastZone.getId());
                    } finally {
                        // Clean up tenant context in async thread
                        TenantContextHolder.clear();
                    }
                }))
                .toList();

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        // Batch save successful blast zones
        if (!successfulBlastZones.isEmpty()) {
            log.info("Batch saving {} successful blast zones", successfulBlastZones.size());
            blastZoneRepository.saveAll(successfulBlastZones);
        }
        if(RECOMPUTE_TASK.equals(taskId)){
            log.info("Recomputation Task No need to update status");
            return;
        }
        // Batch update statuses
        batchUpdateProcessingStatuses(successfulBlastZoneIds, failedBlastZoneIds, taskId);

        log.info("Completed computing attributes for {} blast zones", blastZoneList.size());

        // Check if all tasks are completed
        checkTaskCompletion(taskId);
    }

    /* Computes PML and Creates Mapping between Blast Zone and Locations
     * This method calculates the Potential Maximum Loss (PML) for each location within a blast zone,
     * based on the distance from the blast zone's center and the damage factors.
     * It also creates a mapping between the blast zone and each location, storing relevant details.
     * It also updates the blast zone with the total PML and geocoding resolution.
     * Geocoding resolution is determined based on the minimum geocoding resolution of the locations within the blast zone.
     * @param blastZone The blast zone for which PML is to be computed.
     */
    private void computePML(BlastZone blastZone) {
        Instant start = Instant.now();
        var locationsWithin = locationDAL.findWithinBlastZoneWithDistance(blastZone, BLAST_ZONE_RADIUS);

        if (locationsWithin.isEmpty()) {
            blastZone.setPml(ZERO);
            blastZone.setPmlUtilisation(ZERO);
            return;
        }

        // Pre-filter locations
        List<LocationProjection> validLocations = locationsWithin.stream()
                .filter(location -> location.getDistance() != null)
                .filter(location -> location.getTiv() != null)
                .toList();

        if (validLocations.isEmpty()) {
            blastZone.setPml(ZERO);
            blastZone.setPmlUtilisation(ZERO);
            return;
        }

        // Preload damage factors
        Map<String, DamageFactor> damageFactorCache = preloadDamageFactors(validLocations);

        // Process locations in parallel chunks
        AtomicReference<Double> totalPMLRef = new AtomicReference<>(ZERO);
        AtomicReference<Double> geocodingResolutionMinRef = new AtomicReference<>(HUNDRED);
        List<BlastZoneLocationMapping> allMappings = Collections.synchronizedList(new ArrayList<>());

        // Split locations into chunks for parallel processing
        List<List<LocationProjection>> chunks = partitionList(validLocations, LOCATION_CHUNK_SIZE);

        // Capture tenant context before async operations
        String tenantId = TenantContextHolder.getTenantId();
        String tenantAlias = TenantContextHolder.getTenantAlias();

        List<CompletableFuture<Void>> futures = chunks.stream()
                .map(chunk -> CompletableFuture.runAsync(() -> {
                    try {
                        // Set tenant context in async thread
                        if (tenantId != null) {
                            TenantContextHolder.setTenantContext(tenantId, tenantAlias);
                        }
                        processLocationChunk(chunk, blastZone, damageFactorCache,
                                totalPMLRef, geocodingResolutionMinRef, allMappings);
                    } finally {
                        // Clean up tenant context in async thread
                        TenantContextHolder.clear();
                    }
                }))
                .toList();

        // Wait for all chunks to complete
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        // Set blast zone attributes
        blastZone.setGeocodingResolution(geocodingResolutionMinRef.get());
        blastZone.setGeocoding(getGrade(geocodingResolutionMinRef.get()));
        blastZone.setPml(totalPMLRef.get());
        double pmlUtilisation = getPercentage(totalPMLRef.get(), PML_UTILIZATION_LIMIT);
        blastZone.setPmlUtilisation(pmlUtilisation);

        // Save mappings in batches
        if (!allMappings.isEmpty()) {
            saveMappingsInBatches(allMappings);
        }

        Instant end = Instant.now();
        log.info("Computed PML (optimized) for blast zone {} in {}s", blastZone.getId(), Duration.between(start, end).toSeconds());
    }

    /* Computes Exposure for each account in a blast zone
     * This method calculates the exposure for each blast zone based on Total Insured Value (TIV),
     * limit, excess, and deductible of the accounts under it.
     * It updates the blast zone with the total exposure and earliest expiry date.
     *
     * @param blastZone The blast zone for which exposure is to be computed.
     */
    private void computeExposure(BlastZone blastZone){
        Instant start = Instant.now();
        List<String> submissionIds = locationMappingRepository
                .findSubmissionIdsByBlastZoneIdAndState(blastZone.getId(), BOUND_STATE)
                .stream()
                .map(SubmissionIdOnly::getSubmissionId)
                .toList();

        List<Account> accountList = accountRepository.listAccountsSortedFlexible(submissionIds, null, blastZone.getId());
        log.info("Found {} accounts", accountList.size());
        blastZone.setActive(!accountList.isEmpty());

        double totalExposure = 0.0;
        List<String> expiryDates = new ArrayList<>();

        for (Account acc : accountList) {
            if (!hasValidFields(acc)) continue;

            double exposure = calculateAndSetExposure(acc);
            totalExposure += exposure;

            if (acc.getExpiryDate() != null) {
                expiryDates.add(acc.getExpiryDate());
            }

            log.debug("Account expiry: {}, Exposure: {}", acc.getExpiryDate(), exposure);
        }

        String selectedExpiry = determineRelevantExpiry(expiryDates);
        blastZone.setFirstExpiry(selectedExpiry);
        blastZone.setExposure(totalExposure);
        blastZone.setDfUtilisation(getPercentage(totalExposure, EXPOSURE_UTILIZATION_LIMIT));
        Instant end = Instant.now();
        log.info("Computed Exposure for blast zone {} in {}s", blastZone.getId(), Duration.between(start, end).toSeconds());
    }


    /* Computes PML for a single submission
     * This method calculates the Potential Maximum Loss (PML) for a single submission within a blast zone,
     * based on the distance from the blast zone's center and the damage factors.
     * It also returns the geocoding resolution of the submission.
     *
     * @param blastZone The blast zone for which PML is to be computed.
     * @param submissionId The submission ID for which PML is to be computed.
     * @return AnalysisPML object containing the total PML and geocoding resolution.
     */
    public AnalysisPML computePMLForSubmission(BlastZone blastZone, String submissionId){

        var locationsWithin = locationDAL.findLocationsBySubmissionId(submissionId);
        var locationToDistanceMap = getLocationDistanceMap(blastZone, locationsWithin);
        double totalPML = ZERO;

        //TODO make target group configurable
        final String targetGroup = S_AND_T;

        double geocodingResolutionMin = HUNDRED;
        for (Location location : locationsWithin) {
            Double distance = locationToDistanceMap.get(location.getId());
            if (distance == null) continue;

            String zone = DamageFactorUtils.getDamageFactorZone(distance);
            DamageFactor damageFactor = damageFactorDal.findDamageFactorByZone(zone, targetGroup);
            Double contentsValue = location.getContentsValue()!= null ? location.getContentsValue() : ZERO;
            Double biValue = location.getBiValue12Months()!= null ? location.getBiValue12Months() : ZERO;
            Double buildingValue = location.getBuildingValue()!= null ? location.getBuildingValue() : ZERO;
            if (damageFactor == null ) continue;

            double pml = getPml(contentsValue, biValue, buildingValue, damageFactor);
            totalPML += pml;
            geocodingResolutionMin = Math.min(geocodingResolutionMin,location.getGeocodingResolution());
        }
        return AnalysisPML.builder().pml(totalPML).geoCodingResolution(geocodingResolutionMin).build();
    }
    /* Computes Exposure for a single submission
     * This method calculates the exposure for a single submission within a blast zone,
     * based on the TIV, limit, excess, and deductible of the accounts under it.
     *
     * @param blastZone The blast zone for which exposure is to be computed.
     * @param task The task for which exposure is to be computed.
     * @param submissionId The submission ID for which exposure is to be computed.
     * @param quoteId The quote ID for which exposure is to be computed.
     * @return The total exposure for the submission.
     */
    public double computeExposureForSubmission(BlastZone blastZone, MiAnalysisTask task, String submissionId, String quoteId){

        List<Account>accountList = accountRepository.listAccountsSortedFlexible(Collections.singletonList(submissionId),quoteId,blastZone.getId());
        /* This list should only contain 1 element as we are filtering by submissionId
         * and quoteId, which is unique for each account.
         */
        for (Account acc : accountList) {
            Double tiv = acc.getTiv();
            Double limit = task.getLimit();
            Double excess = task.getExcess();
            Double deductible = task.getDeductible();

            if (tiv == null) continue;

            double calculatedExposure;
            if (tiv > (limit + excess + deductible)) {
                calculatedExposure = limit.intValue();
            } else if (tiv < (excess + deductible)) {
                calculatedExposure = 0;
            } else {
                calculatedExposure = Math.round(tiv - (excess + deductible));
            }
            acc.setExposure(calculatedExposure);
        }


        double exposure =  accountList.stream()
                .map(Account::getExposure)
                .filter(Objects::nonNull)
                .mapToDouble(Double::doubleValue)
                .sum();

        return exposure*(task.getLine()/HUNDRED);
    }

    public void refreshBlastZoneFirstExpiry(BlastZone blastZone) {
        try {
            List<String> submissionIds = locationMappingRepository
                    .findSubmissionIdsByBlastZoneIdAndState(blastZone.getId(), BOUND_STATE)
                    .stream()
                    .map(SubmissionIdOnly::getSubmissionId)
                    .toList();

            if (submissionIds.isEmpty()) {
                log.debug("No submissions found for blast zone {}", blastZone.getId());
                return;
            }

            List<ExpiryDateOnly> expiryProjections = accountRepository.findExpiryDatesBySubmissionIds(submissionIds);
            List<String> expiryDates = expiryProjections.stream()
                    .map(ExpiryDateOnly::getExpiryDate)
                    .filter(Objects::nonNull)
                    .toList();

            String recomputedExpiry = determineRelevantExpiry(expiryDates);

            if (!Objects.equals(recomputedExpiry, blastZone.getFirstExpiry())) {
                blastZone.setFirstExpiry(recomputedExpiry);
            }
        } catch (Exception e) {
            log.error("Error refreshing first expiry for blast zone {}: {}", blastZone.getId(), e.getMessage(), e);
        }
    }


    private void saveMappingsInBatches(List<BlastZoneLocationMapping> mappings) {
        int totalMappings = mappings.size();

        for (int i = 0; i < totalMappings; i += MAPPING_BATCH_SIZE) {
            int endIndex = Math.min(i + MAPPING_BATCH_SIZE, totalMappings);
            List<BlastZoneLocationMapping> batch = mappings.subList(i, endIndex);

            locationMappingDal.upsertAllBlastZoneLocationMappings(batch);

            if (log.isDebugEnabled()) {
                log.debug("Upserted batch {}/{} ({} mappings)",
                        (i / MAPPING_BATCH_SIZE) + 1,
                        (totalMappings + MAPPING_BATCH_SIZE - 1) / MAPPING_BATCH_SIZE,
                        batch.size());
            }
        }
    }

    /**
     * Process a chunk of locations for PML computation
     */
    private void processLocationChunk(List<LocationProjection> chunk, BlastZone blastZone,
                                      Map<String, DamageFactor> damageFactorCache,
                                      AtomicReference<Double> totalPMLRef,
                                      AtomicReference<Double> geocodingResolutionMinRef,
                                      List<BlastZoneLocationMapping> allMappings) {
        double chunkPML = 0.0;
        double chunkGeocodingMin = HUNDRED;
        List<BlastZoneLocationMapping> chunkMappings = new ArrayList<>(chunk.size());

        for (LocationProjection location : chunk) {
            Double distance = location.getDistance();
            String zone = DamageFactorUtils.getDamageFactorZone(distance);
            DamageFactor damageFactor = damageFactorCache.get(zone);

            if (damageFactor == null) continue;

            Double contentsValue = location.getContentsValue()!= null ? location.getContentsValue() : ZERO;
            Double biValue = location.getBiValue12Months()!= null ? location.getBiValue12Months() : ZERO;
            Double buildingValue = location.getBuildingValue()!= null ? location.getBuildingValue() : ZERO;
            Double tiv = location.getTiv();
            Double locationGeocodingResolution = location.getGeocodingResolution();
            String geoCodingGrade = getGrade(locationGeocodingResolution);
            double pml = getPml(contentsValue, biValue, buildingValue, damageFactor);
            double pmlPercentage = getPercentage(pml, tiv);

            // Update chunk totals for BOUND locations
            if (BOUND_STATE.equalsIgnoreCase(location.getState())) {
                chunkPML += pml;
                chunkGeocodingMin = locationGeocodingResolution != null ?
                        Math.min(chunkGeocodingMin, locationGeocodingResolution) : chunkGeocodingMin;
            }

            BlastZoneLocationMapping mapping = BlastZoneLocationMapping.builder()
                    .id(blastZone.getId() + location.getId())
                    .blastZonePml(pml)
                    .blastZoneId(blastZone.getId())
                    .blastZoneTiv(location.getTiv())
                    .pmlZone(zone)
                    .distance(distance)
                    .submissionId(location.getSubmissionId())
                    .geocodingGrade(geoCodingGrade)
                    .locationId(location.getId())
                    .pmlPercentage(pmlPercentage)
                    .state(location.getState())
                    .build();
            chunkMappings.add(mapping);
        }

        // Update global totals atomically
        final double finalChunkPML = chunkPML;
        final double finalChunkGeocodingMin = chunkGeocodingMin;
        totalPMLRef.updateAndGet(current -> current + finalChunkPML);
        geocodingResolutionMinRef.updateAndGet(current -> Math.min(current, finalChunkGeocodingMin));
        allMappings.addAll(chunkMappings);
    }

    /**
     * Compute distances in parallel for better performance
     */
    private Map<String, Double> computeDistancesParallel(BlastZone blastZone, List<Location> locations) {
        Map<String, Double> distanceMap = new ConcurrentHashMap<>();

        // Capture tenant context before async operations
        String tenantId = TenantContextHolder.getTenantId();
        String tenantAlias = TenantContextHolder.getTenantAlias();

        List<CompletableFuture<Void>> futures = locations.stream()
                .map(location -> CompletableFuture.runAsync(() -> {
                    try {
                        // Set tenant context in async thread
                        if (tenantId != null) {
                            TenantContextHolder.setTenantContext(tenantId, tenantAlias);
                        }
                        double distance = DistanceUtils.calculateDistanceInMeters(
                                blastZone.getCentre(), location.getGeometry());
                        distanceMap.put(location.getId(), distance);
                    } finally {
                        // Clean up tenant context in async thread
                        TenantContextHolder.clear();
                    }
                }))
                .toList();

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        return distanceMap;
    }

    /**
     * Preload all damage factors to minimize database calls
     */
    private Map<String, DamageFactor> preloadDamageFactors(List<Location> locations, Map<String, Double> locationToDistanceMap) {
        Set<String> uniqueZones = locations.stream()
                .map(location -> {
                    Double distance = locationToDistanceMap.get(location.getId());
                    return DamageFactorUtils.getDamageFactorZone(distance);
                })
                .collect(toSet());

        return uniqueZones.stream()
                .collect(toMap(
                        zone -> zone,
                        zone -> damageFactorDal.findDamageFactorByZone(zone, S_AND_T),
                        (existing, replacement) -> existing,
                        HashMap::new
                ));
    }

    private Map<String, DamageFactor> preloadDamageFactors(List<LocationProjection> locations) {
        Set<String> uniqueZones = locations.stream()
                .map(location -> {
                    Double distance = location.getDistance();
                    return DamageFactorUtils.getDamageFactorZone(distance);
                })
                .collect(toSet());

        return uniqueZones.stream()
                .collect(toMap(
                        zone -> zone,
                        zone -> damageFactorDal.findDamageFactorByZone(zone, S_AND_T),
                        (existing, replacement) -> existing,
                        HashMap::new
                ));
    }



    private void batchUpdateProcessingStatuses(List<Long> successfulBlastZoneIds,
                                               List<Long> failedBlastZoneIds,
                                               String taskId) {
        try {
            // Batch update successful ones
            if (!successfulBlastZoneIds.isEmpty()) {

                blastZoneProcessingDal.batchUpdateStatusWithBulkOps(
                        successfulBlastZoneIds, taskId, ProcessingStatus.COMPLETED);
            }

            // Batch update failed ones
            if (!failedBlastZoneIds.isEmpty()) {
                blastZoneProcessingDal.batchUpdateStatusWithBulkOps(
                        failedBlastZoneIds, taskId, ProcessingStatus.FAILED);
            }

        } catch (Exception e) {
            log.error("Error during batch status update for task {}: {}", taskId, e.getMessage(), e);
            // Fallback to individual updates if batch fails
            fallbackToIndividualStatusUpdates(successfulBlastZoneIds, failedBlastZoneIds, taskId);
        }
    }
    private void fallbackToIndividualStatusUpdates(List<Long> successfulBlastZoneIds,
                                                   List<Long> failedBlastZoneIds,
                                                   String taskId) {
        log.warn("Falling back to individual status updates for task {}", taskId);

        successfulBlastZoneIds.forEach(blastZoneId -> {
            try {
                blastZoneProcessingDal.updateStatusByBlastZoneIdAndTaskId(
                        blastZoneId, taskId, ProcessingStatus.COMPLETED);
            } catch (Exception e) {
                log.error("Failed to update status for blast zone {}: {}", blastZoneId, e.getMessage());
            }
        });

        failedBlastZoneIds.forEach(blastZoneId -> {
            try {
                blastZoneProcessingDal.updateStatusByBlastZoneIdAndTaskId(
                        blastZoneId, taskId, ProcessingStatus.FAILED);
            } catch (Exception e) {
                log.error("Failed to update status for blast zone {}: {}", blastZoneId, e.getMessage());
            }
        });
    }


    private void checkTaskCompletion(String taskId){
        boolean stillInProgress = blastZoneProcessingDal.isInProgress(taskId);
        if (!stillInProgress) {
            log.info("All tasks for taskId {} have been completed", taskId);
            Optional<WorkflowTask> optional = taskRepository.findById(taskId);
            if (optional.isPresent()){
                WorkflowTask task = optional.get();
                task.setStatus(ProcessingStatus.COMPLETED);
                task.setUpdatedOn(Instant.now());
                taskRepository.save(task);
                log.info("Task {} has been marked as completed", taskId);
            } else {
                log.warn("No task found with ID {}", taskId);
            }
            eventSender.sendTaskCallBack(taskId);
        } else {
            log.warn("Some tasks for taskId {} are still in progress", taskId);
        }
    }

}

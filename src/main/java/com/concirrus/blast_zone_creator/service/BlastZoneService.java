package com.concirrus.blast_zone_creator.service;


import ch.hsr.geohash.GeoHash;
import com.concirrus.blast_zone_creator.dal.LocationDAL;
import com.concirrus.blast_zone_creator.dal.blastzone.BlastZoneDAL;
import com.concirrus.blast_zone_creator.dal.blastzone.BlastZoneLocationMappingDal;
import com.concirrus.blast_zone_creator.dal.blastzone.BlastZoneLocationMappingRepository;
import com.concirrus.blast_zone_creator.dal.blastzone.BlastZoneRepository;
import com.concirrus.blast_zone_creator.dto.workflow.LocationEventDTO;
import com.concirrus.blast_zone_creator.model.Location;
import com.concirrus.blast_zone_creator.model.blastzone.BlastZone;
import com.concirrus.blast_zone_creator.utils.BlastZoneUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.geo.Point;
import org.springframework.data.mongodb.core.geo.GeoJsonPoint;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.concirrus.blast_zone_creator.config.Constants.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class BlastZoneService {

    private final LocationDAL locationDAL;
    private final BlastZoneRepository blastZoneRepository;
    private final BlastZoneLocationMappingRepository blastZoneLocationMappingRepository;
    private final BlastZoneLocationMappingDal blastZoneLocationMappingDal;
    private final BlastZoneDAL blastZoneDAL;



    public List<BlastZone> createBlastZone(LocationEventDTO event){
        Location location = locationDAL.getLocationById(event.getLocationId());
        try {
            return  createBlastZonesForLocation(location);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    public List<Long> findBlastZonesForLocation(String locationId) {
        return blastZoneLocationMappingDal.findBlastZoneIdsByLocationId(locationId);
    }

    public List<Long> findBlastZoneIdsNearLocation(double longitude, double latitude) {

        return blastZoneDAL.findBlastZoneIdsNear(longitude, latitude, BLAST_ZONE_RADIUS);
    }

    public boolean deleteBlastZoneLocationMapping(String locationId, String submissionId) {
        try {
             blastZoneLocationMappingDal.deleteBlastZoneLocationMappingByLocationAndSubmissionId(locationId, submissionId);
            return true;
        } catch (Exception e) {
            log.error("Error deleting Blast Zone Location Mapping for submissionId: {} and locationId: {}", submissionId, locationId, e);
            return false;
        }
    }

    public boolean deleteBlastZoneLocationMappingBySubmissionId(String submissionId) {
        try {
            blastZoneLocationMappingDal.deleteBlastZoneLocationMappingBySubmissionId(submissionId);
            return true;
        } catch (Exception e) {
            log.error("Error deleting Blast Zone Location Mapping for submissionId: {}", submissionId, e);
            return false;
        }
    }


    public void saveBlastZones(List<BlastZone> blastZones) {
        blastZoneRepository.saveAll(blastZones);
    }

    public Page<BlastZone> getBlastZoneWithExpiredFirstExpiry(int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        String today = LocalDate.now().toString();
        return blastZoneRepository.findByFirstExpiryBefore(today, pageable);
    }

    private List<BlastZone> createBlastZonesForLocation(Location loc) throws Exception{
        Set<Point> blastZoneCentres = BlastZoneUtils.getBlastZoneCentres(loc.getLatitude(), loc.getLongitude());
        List<BlastZone> blastZones = new ArrayList<>();
        for(var blastZoneCentre: blastZoneCentres) {
            //var blastZonePolygon = BlastZoneUtils.getBlastZone(blastZoneCentre);
            long id = GeoHash.withCharacterPrecision(blastZoneCentre.getY(), blastZoneCentre.getX(), 12).longValue();
            var blastZone = BlastZone.builder()
                    .centre(new GeoJsonPoint(blastZoneCentre))
                    //.geometry(blastZonePolygon)
                    .id(id)
                    .name(BLAST_ZONE + SEPARATOR + id)
                    .build();


            blastZones.add(blastZone);
        }
        filterOutAlreadyExistingBlastZonesAndSave(blastZones);
        return blastZones;
    }

    public Page<BlastZone> findAll(int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return blastZoneRepository.findAll(pageable);
    }

    private void filterOutAlreadyExistingBlastZonesAndSave(List<BlastZone> blastZones) {
        if (blastZones.isEmpty()) return;

        Set<Long> candidateIds = blastZones.stream()
                .map(BlastZone::getId)
                .collect(Collectors.toSet());

        Set<Long> existingIds = blastZoneDAL.findExistingBlastZoneIds(candidateIds);

        List<BlastZone> newBlastZones = blastZones.stream()
                .filter(blastZone -> !existingIds.contains(blastZone.getId()))
                .toList();
        blastZoneRepository.saveAll(newBlastZones);
    }

}

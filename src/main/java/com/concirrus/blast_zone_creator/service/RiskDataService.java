package com.concirrus.blast_zone_creator.service;

import com.concirrus.blast_zone_creator.dto.RiskDataDTO;
import com.concirrus.blast_zone_creator.dto.riskData.RiskDataItem;
import com.concirrus.blast_zone_creator.dto.riskData.SandPRiskResponse;
import com.concirrus.blast_zone_creator.model.blastzone.BlastZone;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@Service
@RequiredArgsConstructor
public class RiskDataService {

    private static final String SANDP_API_URL = "http://sandp-adapter-service/sandp/risk-data";
    public static final String EPOP = "EPOP";


    private final RestTemplate restTemplate;


    private final ObjectMapper objectMapper;

    public boolean enrichBlastZonesWithRiskData(List<BlastZone> blastZones) {
        try {
            // Create request body from blast zones
            List<Map<String, Object>> requestBody = createRequestBody(blastZones);

            // Make API call
            SandPRiskResponse response = callSandPRiskApi(requestBody);
            // Enrich blast zones with response data
            enrichBlastZones(blastZones, response);
            return true;

        } catch (Exception e) {
            log.error("Error enriching blast zones with risk data: {}", e.getMessage(), e);
            return false;
        }
    }

    private List<Map<String, Object>> createRequestBody(List<BlastZone> blastZones) {
        return blastZones.stream()
                .map(zone -> {
                    Map<String, Object> request = new HashMap<>();
                    request.put("locationId", String.valueOf(zone.getId()));
                    request.put("latitude", zone.getCentre().getCoordinates().get(1));
                    request.put("longitude", zone.getCentre().getCoordinates().get(0));
                    request.put("type", EPOP);
                    return request;
                })
                .collect(Collectors.toList());
    }

    private SandPRiskResponse callSandPRiskApi(List<Map<String, Object>> requestBody) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<List<Map<String, Object>>> entity = new HttpEntity<>(requestBody, headers);

            ResponseEntity<SandPRiskResponse> response = restTemplate.exchange(
                    SANDP_API_URL,
                    HttpMethod.POST,
                    entity,
                    SandPRiskResponse.class
            );

            return response.getBody();
        } catch (Exception e) {
            log.error("Error calling SandP risk API: {}", e.getMessage(), e);
            SandPRiskResponse response = new SandPRiskResponse();
            response.setSuccess(false);
            response.setMessage(e.getMessage());
            response.setData(List.of());
            return response;
        }
    }

    private void enrichBlastZones(List<BlastZone> blastZones, SandPRiskResponse response) {
        // Create a map for quick lookup by locationId
        Map<String, RiskDataItem> riskDataMap = response.getData().stream()
                .collect(Collectors.toMap(RiskDataItem::getLocationId, item -> item));

        // Enrich each blast zone with corresponding risk data
        blastZones.stream()
                .peek(zone -> {
                    RiskDataItem riskData = riskDataMap.get(String.valueOf(zone.getId()));
                    if (riskData != null) {
                        zone.setRiskData(RiskDataDTO.fromRiskData(riskData.getRiskData()));
                    }else {
                        log.warn("No risk data found for blast zone: {}", zone.getId());
                        zone.setRiskData(RiskDataDTO.empty());
                    }
                })
                .toList();
    }



}



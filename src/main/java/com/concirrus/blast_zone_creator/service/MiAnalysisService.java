package com.concirrus.blast_zone_creator.service;


import com.concirrus.blast_zone_creator.dal.miAnalysis.MiAnalysisJobDal;
import com.concirrus.blast_zone_creator.dal.miAnalysis.MiAnalysisJobRepository;
import com.concirrus.blast_zone_creator.dal.miAnalysis.MiAnalysisResultDal;
import com.concirrus.blast_zone_creator.dto.enums.ProcessingStatus;
import com.concirrus.blast_zone_creator.dto.miAnalysis.MiAnalysisJobRequest;
import com.concirrus.blast_zone_creator.dto.rest.CustomPageResponse;
import com.concirrus.blast_zone_creator.model.miAnalysis.MiAnalysisJob;
import com.concirrus.blast_zone_creator.model.miAnalysis.MiAnalysisResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.*;

import static com.concirrus.blast_zone_creator.config.Constants.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class MiAnalysisService {


    private final MiAnalysisJobDal jobDal;
    private final MiAnalysisResultDal resultDal;
    private final MiAnalysisJobRepository miAnalysisjobRepository;
    private final TaskExecutionService taskExecutionService;
    public MiAnalysisJob getMiAnalysisJob(String jobId) {
            log.info("Retrieving MiAnalysisJob with ID: {}", jobId);
            return jobDal.getMiAnalysisJobById(jobId);
    }

    public Map<String, String> getJobForAccount(String submissionId, String quoteId) {
        log.info("Retrieving MiAnalysisJob for submissionId: {} and quoteId: {}", submissionId.replaceAll("[\r\n]", ""), quoteId.replaceAll("[\r\n]", ""));
        Optional<MiAnalysisJob> optional =  miAnalysisjobRepository.findFirstBySubmissionIdAndQuoteIdOrderByCreatedAtDesc(submissionId, quoteId);
        if (optional.isPresent()) {
            log.info("Found MiAnalysisJob with ID: {}", optional.get().getJobId());
            Map<String,String>result = new HashMap<>();
            result.put("jobId", optional.get().getJobId());
            result.put("status", optional.get().getStatus().name());
            return result;
        } else {
            log.warn("No MiAnalysisJob found for submissionId: {} and quoteId: {}", submissionId.replaceAll("[\r\n]", ""), quoteId.replaceAll("[\r\n]", ""));
            return null;
        }
    }

    public CustomPageResponse<MiAnalysisResult> miAnalysisResults(String jobId, String peril, String sortBy, String sortOrder, int page, int size) {

        MiAnalysisJob job = jobDal.getMiAnalysisJobById(jobId);
        if (job == null) {
            log.warn("MiAnalysisJob with ID {} not found", jobId);
            return CustomPageResponse.empty(page); // assume you have a static method to create an empty response
        }

        String taskId = job.getJobId() + SEPARATOR + peril;

        long total = resultDal.countAnalysisResults(taskId);
        List<MiAnalysisResult> results = resultDal.findAnalysisResults(taskId, sortBy, sortOrder, page, size);

        return CustomPageResponse.of(results, total, page, (long) (page + 1) * size < total);
    }

    public MiAnalysisJob createMiAnalysisJob(MiAnalysisJobRequest request) {
        List<String> validationErrors = validate(request);
        if (!validationErrors.isEmpty()) {
            log.error("Validation errors occurred: {}", validationErrors);
            throw new IllegalArgumentException("Invalid MiAnalysisJobRequest: " + String.join(", ", validationErrors));
        }

        MiAnalysisJob job = new MiAnalysisJob();
        job.setJobId(UUID.randomUUID().toString());

        job.setStatus(ProcessingStatus.CREATED);


        job.setSubmissionId(request.getSubmissionId());
        job.setQuoteId(request.getQuoteId());
        job.setCreatedAt(Instant.now());
        taskExecutionService.createTasks(job, request);
        miAnalysisjobRepository.save(job);
        log.info("Created MiAnalysisJob with ID: {}", job.getJobId());

        if (Boolean.TRUE.equals(request.getInitiateJob())) {
            processJob(job.getJobId());
        } else {
            log.info("MiAnalysisJob with ID: {} created but not initiated", job.getJobId());
        }

        return job;
    }

    public String processJob(String jobId){
        taskExecutionService.processJob(jobId);
        log.info("Started processing for MiAnalysisJob with ID: {}", jobId);
        return "JOB Started for Job_Id:" + jobId;
    }





    public static List<String> validate(MiAnalysisJobRequest request) {
        List<String> errors = new ArrayList<>();
        log.info("Validating MiAnalysisJobRequest: {}", request);
        if (request.getPerils() == null || request.getPerils().isEmpty()) {
            errors.add("Perils list must not be empty.");
        } else {
            for (String peril : request.getPerils()) {
                switch (peril.toLowerCase()) {
                    case SRCC:
                        if (request.getSrcc() == null) {
                            errors.add("SRCC value must be provided when 'srcc' peril is selected.");
                        }
                        break;
                    case WAR:
                        if (request.getWar() == null) {
                            errors.add("War value must be provided when 'war' peril is selected.");
                        }
                        break;
                    case SANDT:
                        if (request.getSAndT() == null) {
                            errors.add("S&T value must be provided when 'sAndT' peril is selected.");
                        }
                        break;
                    default:
                        errors.add("Invalid peril type: " + peril);
                }
            }
        }

        if (request.getExcess() == null || request.getExcess() < 0) {
            errors.add("Excess must be provided and be non-negative.");
        }

        if (request.getLine() == null || request.getLine() <= 0) {
            errors.add("Line must be provided and greater than 0.");
        }

        if (request.getDeductible() < 0) {
            errors.add("Deductible must be non-negative.");
        }

        return errors;
    }

}

package com.concirrus.blast_zone_creator.service;


import com.concirrus.blast_zone_creator.dto.submission.ClientConfigResponse;
import com.concirrus.blast_zone_creator.sal.AccessManagementSal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@Slf4j
@RequiredArgsConstructor
public class TenantServiceImp implements TenantService{

    private final AccessManagementSal accessManagementSal;

    @Value("${tenant-ids}")
    private List<String> validTenantIds;

    @Override
    public String getTenantAlias(String clientId) {
        ClientConfigResponse clientConfigResponse = accessManagementSal.getClientConfigByClientId(clientId);
        return clientConfigResponse.getTenantName();
    }

    @Override
    public String getAuthTokenForTenant(String clientId) {
        return accessManagementSal.getToken(clientId);
    }

    /* checks if aggregation is enabled for the tenant
    this is done by checking if the tenant id is in the list of valid tenant ids,
    TODO: do this using tenant manager service once available
    */
    @Override
    public boolean isValidTenantId(String clientId) {
        return validTenantIds.contains(clientId);
    }

    @Override
    public List<String> getValidTenantIds() {
        return validTenantIds;
    }

}

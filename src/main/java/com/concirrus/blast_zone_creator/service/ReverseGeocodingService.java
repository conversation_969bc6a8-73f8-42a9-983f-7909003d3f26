package com.concirrus.blast_zone_creator.service;

import com.concirrus.blast_zone_creator.dal.blastzone.BlastZoneDAL;
import com.concirrus.blast_zone_creator.dal.blastzone.BlastZoneRepository;
import com.concirrus.blast_zone_creator.model.blastzone.BlastZone;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

import static com.concirrus.blast_zone_creator.config.Constants.BLAST_ZONE;
import static com.concirrus.blast_zone_creator.config.Constants.SEPARATOR;

@Slf4j
@Service
@RequiredArgsConstructor
public class ReverseGeocodingService {


    private static final int BATCH_SIZE = 100;
    private static final int MAX_RETRIES = 3;
    private final ObjectMapper objectMapper ;
    private final RestTemplate restTemplate ;


    public void geocodeBlastZones(List<BlastZone> blastZones) {
        if (blastZones.isEmpty()) {
            log.info("No blast zones needing geocoding at this time.");
            return;
        }

    }

}
package com.concirrus.blast_zone_creator.service;

import com.concirrus.blast_zone_creator.dal.blastzone.BlastZoneDAL;
import com.concirrus.blast_zone_creator.dal.blastzone.BlastZoneRepository;
import com.concirrus.blast_zone_creator.dto.geocoding.GeocodingRequest;
import com.concirrus.blast_zone_creator.dto.geocoding.GeocodingResponse;
import com.concirrus.blast_zone_creator.model.blastzone.BlastZone;
import com.concirrus.blast_zone_creator.sal.GeocodingSal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.geo.GeoJsonPoint;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ReverseGeocodingService {

    private static final int BATCH_SIZE = 100;

    private final GeocodingSal geocodingSal;
    private final BlastZoneRepository blastZoneRepository;


    public void geocodeBlastZones(List<BlastZone> blastZones) {
        if (blastZones.isEmpty()) {
            log.info("No blast zones needing geocoding at this time.");
            return;
        }

        log.info("Starting geocoding process for {} blast zones", blastZones.size());

        // Process blast zones in batches
        List<List<BlastZone>> batches = partitionList(blastZones, BATCH_SIZE);

        for (int batchIndex = 0; batchIndex < batches.size(); batchIndex++) {
            List<BlastZone> batch = batches.get(batchIndex);
            log.info("Processing batch {} of {} (size: {})", batchIndex + 1, batches.size(), batch.size());

            try {
                processBatch(batch);
            } catch (Exception e) {
                log.error("Error processing batch {} of blast zones: {}", batchIndex + 1, e.getMessage(), e);
                // Continue with next batch even if current batch fails
            }
        }

        log.info("Completed geocoding process for {} blast zones", blastZones.size());
    }

    private void processBatch(List<BlastZone> blastZones) {
        // Filter blast zones that have coordinates and need geocoding
        List<BlastZone> validBlastZones = blastZones.stream()
                .filter(this::hasValidCoordinates)
                .collect(Collectors.toList());

        if (validBlastZones.isEmpty()) {
            log.warn("No valid blast zones with coordinates found in batch");
            return;
        }

        // Create geocoding requests
        List<GeocodingRequest> requests = validBlastZones.stream()
                .map(this::createGeocodingRequest)
                .collect(Collectors.toList());

        try {
            // Call geocoding service
            List<GeocodingResponse> responses = geocodingSal.batchReverseGeocode(requests);

            // Update blast zones with geocoding results
            updateBlastZonesWithGeocodingResults(validBlastZones, responses);

            // Save updated blast zones
            blastZoneRepository.saveAll(validBlastZones);

            log.info("Successfully geocoded and saved {} blast zones", validBlastZones.size());

        } catch (Exception e) {
            log.error("Error during geocoding batch processing: {}", e.getMessage(), e);
            throw e;
        }
    }

    private boolean hasValidCoordinates(BlastZone blastZone) {
        return
                blastZone.getCentre().getX() >= -180.0
                && blastZone.getCentre().getX() <= 180.0
                && blastZone.getCentre().getY() >= -90.0
                && blastZone.getCentre().getY() <= 90.0;
    }

    private GeocodingRequest createGeocodingRequest(BlastZone blastZone) {
        return GeocodingRequest.builder()
                .id(blastZone.getId())
                .latitude(blastZone.getCentre().getY())
                .longitude(blastZone.getCentre().getX())
                .build();
    }

    private void updateBlastZonesWithGeocodingResults(List<BlastZone> blastZones, List<GeocodingResponse> responses) {
        // Create a map for quick lookup of responses by ID
        Map<Long, GeocodingResponse> responseMap = responses.stream()
                .collect(Collectors.toMap(GeocodingResponse::getId, response -> response));

        for (BlastZone blastZone : blastZones) {
            GeocodingResponse response = responseMap.get(blastZone.getId());
            if (response != null) {
                updateBlastZoneWithGeocodingData(blastZone, response);
            } else {
                blastZone.setNeedsGeocoding(false);
                log.warn("No geocoding response found for blast zone ID: {}", blastZone.getId());
            }
        }
    }

    private void updateBlastZoneWithGeocodingData(BlastZone blastZone, GeocodingResponse response) {
        // Set full address
        blastZone.setFullAddress(response.getFullAddress());

        // Set postal code
        blastZone.setPostalCode(response.getPostalCode());

        // Set city and country if not already set or if geocoding provides better data
        if (!StringUtils.hasText(blastZone.getCity()) || StringUtils.hasText(response.getCity())) {
            blastZone.setCity(response.getCity());
        }

        if (!StringUtils.hasText(blastZone.getCountry()) || StringUtils.hasText(response.getCountry())) {
            blastZone.setCountry(response.getCountry());
        }

        // Set blast zone name as "postalCode | city | country"
        String name = buildBlastZoneName(response.getPostalCode(), response.getCity(), response.getCountry());
        blastZone.setName(name);

        // Mark as no longer needing geocoding
        blastZone.setNeedsGeocoding(false);


        log.debug("Updated blast zone {} with geocoding data: {}", blastZone.getId(), name);
    }

    private String buildBlastZoneName(String postalCode, String city, String country) {
        List<String> nameParts = new ArrayList<>();

        if (StringUtils.hasText(postalCode)) {
            nameParts.add(postalCode.trim());
        }

        if (StringUtils.hasText(city)) {
            nameParts.add(city.trim());
        }

        if (StringUtils.hasText(country)) {
            nameParts.add(country.trim());
        }

        return nameParts.isEmpty() ? "Unknown Location" : String.join(" | ", nameParts);
    }

    private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        List<List<T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            partitions.add(list.subList(i, Math.min(i + batchSize, list.size())));
        }
        return partitions;
    }

}
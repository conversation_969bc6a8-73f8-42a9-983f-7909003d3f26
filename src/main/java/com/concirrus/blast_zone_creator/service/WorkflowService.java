package com.concirrus.blast_zone_creator.service;

import com.concirrus.blast_zone_creator.config.TenantContextHolder;
import com.concirrus.blast_zone_creator.dal.blastzone.BlastZoneDAL;
import com.concirrus.blast_zone_creator.dal.blastzone.BlastZoneLocationMappingDal;
import com.concirrus.blast_zone_creator.dal.workflow.BlastZoneProcessingStatusRepository;
import com.concirrus.blast_zone_creator.dal.workflow.TaskRepository;
import com.concirrus.blast_zone_creator.dto.enums.Attributes;
import com.concirrus.blast_zone_creator.dto.enums.ProcessingStatus;
import com.concirrus.blast_zone_creator.dto.enums.UpdateType;
import com.concirrus.blast_zone_creator.dto.workflow.BlastZoneEventDTO;
import com.concirrus.blast_zone_creator.dto.workflow.LocationEventDTO;
import com.concirrus.blast_zone_creator.eventing.producer.EventSender;
import com.concirrus.blast_zone_creator.model.blastzone.BlastZone;
import com.concirrus.blast_zone_creator.model.workflow.BlastZoneProcessingStatus;
import com.concirrus.blast_zone_creator.model.workflow.WorkflowTask;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static com.concirrus.blast_zone_creator.config.Constants.BOUND_STATE;

@Service
@RequiredArgsConstructor
@Slf4j
public class WorkflowService {

    private final BlastZoneService blastZoneService;
    private final EventSender eventSender;
    private final AttributeComputationService attributeComputationService;
    private final BlastZoneDAL blastZoneDAL;
    private final TaskRepository  taskRepository;
    private final BlastZoneProcessingStatusRepository blastZoneProcessingStatusRepository;
    private final BlastZoneLocationMappingDal blastZoneLocationMappingDal;

    // Process submission updates to create or update blast zones
    public void processSubmissionUpdate(LocationEventDTO eventDTO){

        //Step 1 create new blast zones
        List<Long>blastZoneIds;

        if (UpdateType.SUBMISSION_REVIEWED.equals(eventDTO.getUpdateType())){
            List<BlastZone> newBlastZonesList =  blastZoneService.createBlastZone(eventDTO);
            blastZoneIds = newBlastZonesList.stream().map(BlastZone::getId).toList();
        }else {
            blastZoneIds = blastZoneService.findBlastZonesForLocation(eventDTO.getLocationId());
        }

        if(UpdateType.SUBMISSION_BOUND.equals(eventDTO.getUpdateType())){
            log.info("Marking the location mappings a bound");
            blastZoneLocationMappingDal.updateStateForLocationId(eventDTO.getLocationId(), BOUND_STATE);
        }

        if(UpdateType.SUBMISSION_DELETED.equals(eventDTO.getUpdateType())){
            log.info("Deleting the location mappings for deleted submission");
            blastZoneService.deleteBlastZoneLocationMapping(eventDTO.getLocationId(), eventDTO.getSubmissionId());
        }
        WorkflowTask task = createTask(eventDTO,blastZoneIds);
        //Step 2 publish blast zone events in batches
        sendBlastZoneEventsInBatches(blastZoneIds, eventDTO, task);
    }

    //  Account CREATED,UPDATED, DELETED
    public void processAccountUpdate(LocationEventDTO event){
        //Step 1 find blast zones for the quote
        List<Long> blastZoneIds  = blastZoneService.findBlastZonesForLocation(event.getLocationId());
        WorkflowTask task = createTask(event,blastZoneIds);
        if (blastZoneIds.isEmpty()) {
            log.warn("No blast zones found for submission: {}", event.getSubmissionId());
            return;
        }
        //Step 2 send blast zone processing message
        log.info("Processing new account for blast zones: {}", blastZoneIds.size());
        BlastZoneEventDTO blastZoneEvent = new BlastZoneEventDTO();
        blastZoneEvent.setBlastZoneIds(blastZoneIds);
        blastZoneEvent.setAttributesToRecompute(Attributes.EXPOSURE);
        blastZoneEvent.setTaskId(task.getTaskId());
        blastZoneEvent.setClientId(TenantContextHolder.getTenantId());
        eventSender.sendBlastZoneProcessingMessage(blastZoneEvent);

    }

    // CREATE, UPDATE, DELETE ,Process location updates to find blast zones and send processing messages
    public void processLocationUpdate(LocationEventDTO event){
        //Step 1 find blast zones for the location
        List<Long> blastZoneIds = new ArrayList<>();

        if (UpdateType.LOCATION_CREATED.equals(event.getUpdateType())){
            log.info("Location {} created starting to create blast zone mapping for this location Id", event.getLocationId());
            List<BlastZone> newBlastZonesList = blastZoneService.createBlastZone(event);
            blastZoneIds = newBlastZonesList.stream().map(BlastZone::getId).toList();
            if (blastZoneIds.isEmpty()) {
                log.warn("No blast zones created for location: {}", event.getLocationId());
                return;
            }
        } else {
            // For updates or deletions, we find existing blast zones
            log.info("Finding existing blast zones for location: {}", event.getLocationId());
            blastZoneIds = blastZoneService.findBlastZonesForLocation(event.getLocationId());
        }

        WorkflowTask task = createTask(event,blastZoneIds);
        if (blastZoneIds.isEmpty()) {
            log.warn("No blast zones found for location: {}", event.getLocationId());
            return;
        }



        //Step 2 Check if the event is for a location Delete
        if (UpdateType.LOCATION_DELETED.equals(event.getUpdateType())) {
            log.info("Location {} deleted starting to delete blast zone mapping for this location Id", event.getLocationId());
            boolean result = blastZoneService.deleteBlastZoneLocationMapping(event.getLocationId(), event.getSubmissionId());
            if (result) {
                log.info("Blast zone location mapping deleted for location Id: {}", event.getLocationId());
            } else {
                log.error("Failed to delete blast zone location mapping for location Id: {}", event.getLocationId());
            }
        }


        //Step 3 send blast zone processing message
        log.info("Processing location update for blast zones: {}", blastZoneIds.size());
        BlastZoneEventDTO blastZoneEvent = new BlastZoneEventDTO();
        blastZoneEvent.setBlastZoneIds(blastZoneIds);
        blastZoneEvent.setClientId(TenantContextHolder.getTenantId());
        blastZoneEvent.setAttributesToRecompute(event.getState().equalsIgnoreCase(BOUND_STATE) ? Attributes.ALL : Attributes.PML);
        blastZoneEvent.setTaskId(task.getTaskId());
        blastZoneEvent.setReverseGeocodingEnabled(UpdateType.LOCATION_CREATED.equals(event.getUpdateType()));
        eventSender.sendBlastZoneProcessingMessage(blastZoneEvent);
    }

    //Process Location Coordinate Update
    /* Location coordinates have been updated but the locationId is same
    We can delete old mappings and create new ones with new coordinates
     */
    public void processLocationCoordinateUpdate(LocationEventDTO event){
        //Step 1 find blast zones for the location
        String locationId = event.getLocationId();
        String submissionId = event.getSubmissionId();
        blastZoneService.deleteBlastZoneLocationMapping(locationId, submissionId);
        List<BlastZone> blastZones = blastZoneService.createBlastZone(event);
        List<Long> blastZoneIds = blastZones.stream().map(BlastZone::getId).toList();
        WorkflowTask task = createTask(event,blastZoneIds);
        if (blastZoneIds.isEmpty()) {
            log.warn("No blast zones found for location: {}", event.getLocationId());
            return;
        }
        //Step 2 send blast zone processing message
        log.info("Processing location coordinate update for blast zones: {}", blastZoneIds.size());
        BlastZoneEventDTO blastZoneEvent = new BlastZoneEventDTO();
        blastZoneEvent.setBlastZoneIds(blastZoneIds);
        blastZoneEvent.setClientId(TenantContextHolder.getTenantId());
        blastZoneEvent.setAttributesToRecompute(Attributes.PML);
        blastZoneEvent.setTaskId(task.getTaskId());
        eventSender.sendBlastZoneProcessingMessage(blastZoneEvent);
    }


    public void computeAttributes(BlastZoneEventDTO event){
        //Step 1 find blast zones
        List<Long> blastZoneIds = event.getBlastZoneIds();
        if (blastZoneIds.isEmpty()) {
            log.warn("No blast zones found for submission bound: {}", event.getBlastZoneIds());
            return;
        }
        log.info("Processing submission bound for blast zones: {}", blastZoneIds.size());
        //Step 2 compute attributes for blast zones
        try {
            // TODO: Needs to be streamed to avoid memory issues with large count of blast zones
            attributeComputationService.computeAttributesForBlastZones(blastZoneDAL.getBlastZonesByIds(blastZoneIds), event.getAttributesToRecompute(), event.getTaskId());
        } catch (Exception e) {
            log.error("Error computing attributes for blast zones: {}", blastZoneIds, e);
            throw new RuntimeException(e);
        }
    }

    private WorkflowTask createTask(LocationEventDTO eventDTO,List<Long>blastZoneIds) {
        WorkflowTask workflowTask = new WorkflowTask();
        workflowTask.setTaskEntity("Location");
        workflowTask.setTaskEntityId(eventDTO.getLocationId());
        workflowTask.setUpdateType(eventDTO.getUpdateType());
        workflowTask.setTaskId(UUID.randomUUID().toString());
        workflowTask.setJobId(eventDTO.getJobId());
        workflowTask.setCreatedOn(Instant.now());
        workflowTask.setStatus(ProcessingStatus.IN_PROGRESS);
        workflowTask.setBlastZonesToProcess(blastZoneIds.size());
        // Save the task to the database
        taskRepository.save(workflowTask);
        log.info("Created workflow task for event: {}, with blast zones: {}", eventDTO, blastZoneIds.size());
        List<BlastZoneProcessingStatus> blastZoneProcessingStatuses =  blastZoneIds.stream().map(blastZoneId -> {
                BlastZoneProcessingStatus blastZoneProcessingStatus = new BlastZoneProcessingStatus();
                blastZoneProcessingStatus.setBlastZoneId(blastZoneId);
                blastZoneProcessingStatus.setTaskId(workflowTask.getTaskId());
                blastZoneProcessingStatus.setStatus(ProcessingStatus.IN_PROGRESS);
                blastZoneProcessingStatus.setCreatedAt(Instant.now());
                return blastZoneProcessingStatus;
        }).toList();

       blastZoneProcessingStatusRepository.saveAll(blastZoneProcessingStatuses);
       return workflowTask;
    }


    private void sendBlastZoneEventsInBatches(List<Long> blastZoneIds, LocationEventDTO eventDTO, WorkflowTask task) {
        if (blastZoneIds == null || blastZoneIds.isEmpty()) {
            log.info("No blast zone IDs to process.");
            return;
        }

        final int batchSize = 20;
        boolean reverseGeocodingEnabled = UpdateType.SUBMISSION_REVIEWED.equals(eventDTO.getUpdateType());

        for (int i = 0; i < blastZoneIds.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, blastZoneIds.size());
            List<Long> batchIds = blastZoneIds.subList(i, endIndex);

            BlastZoneEventDTO blastZoneEvent = BlastZoneEventDTO.builder()
                    .blastZoneIds(batchIds)
                    .attributesToRecompute(eventDTO.getState().equals(BOUND_STATE) ? Attributes.ALL : Attributes.PML)
                    .reverseGeocodingEnabled(reverseGeocodingEnabled)
                    .taskId(task.getTaskId())
                    .clientId(eventDTO.getClientId())
                    .build();

            eventSender.sendBlastZoneProcessingMessage(blastZoneEvent);
            log.debug("Sent batch {}-{} of {} blast zone IDs", i + 1, endIndex, blastZoneIds.size());
        }

        log.info("Sent {} blast zone IDs in {} batches for processing",
                blastZoneIds.size(),
                (int) Math.ceil((double) blastZoneIds.size() / batchSize));
    }




}

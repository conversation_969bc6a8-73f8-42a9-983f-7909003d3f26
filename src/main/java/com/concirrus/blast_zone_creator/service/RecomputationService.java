package com.concirrus.blast_zone_creator.service;


import com.concirrus.blast_zone_creator.dto.enums.Attributes;
import com.concirrus.blast_zone_creator.model.blastzone.BlastZone;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

import static com.concirrus.blast_zone_creator.config.Constants.RECOMPUTE_TASK;


@Service
@Slf4j

public class RecomputationService {

    private static final int BATCH_SIZE = 500;
    private static final int RECOMPUTATION_BATCH_SIZE = 20;
    private final BlastZoneService blastZoneService;
    private final AttributeComputationService attributeComputationService;

    private final ExecutorService executorService;

    public RecomputationService(BlastZoneService blastZoneService, AttributeComputationService attributeComputationService, @Qualifier("virtualThreadExecutor") ExecutorService executorService) {
        this.blastZoneService = blastZoneService;
        this.attributeComputationService = attributeComputationService;
        this.executorService = executorService;
    }

    @Async
    public void recomputeBlastZoneAttributes() {
        log.info("Starting recomputation of blast zone attributes");
        try {
            int page = 0;
            int totalProcessed = 0;
            while (true) {
                Page<BlastZone> blastZonePage = blastZoneService.findAll(page, RECOMPUTATION_BATCH_SIZE);
                if (blastZonePage.isEmpty()) {
                    break;
                }
                List<BlastZone> blastZones = blastZonePage.getContent();
                log.info("Processing batch {} with {} blast zones for recomputation", page, blastZones.size());
                attributeComputationService.computeAttributesForBlastZones(blastZones, Attributes.ALL, RECOMPUTE_TASK);
                page++;
                totalProcessed += blastZones.size();
                log.info("Recomputed attributes for {} blast zones", totalProcessed);
            }
            log.info("Recomputation of blast zone attributes completed");
        }
        catch (Exception e) {
            log.error("Error recomputing blast zone attributes: {}", e.getMessage(), e);
        }
    }

    @Async
    public void recomputeFirstExpiryForBlastZones() {
        log.info("Starting recomputation of first expiry for blast zones");
        int page = 0;
        int totalProcessed = 0;
        try {
            while (true) {

                Page<BlastZone> blastZonePage = blastZoneService.getBlastZoneWithExpiredFirstExpiry(page, BATCH_SIZE);
                if (blastZonePage.isEmpty()) {
                    break;
                }
                List<BlastZone> blastZones = blastZonePage.getContent();
                log.info("Processing batch {} with {} blast zones", page, blastZones.size());
                List<CompletableFuture<Void>> futures = blastZones.stream()
                        .map(blastZone -> CompletableFuture.runAsync(() -> {
                            attributeComputationService.refreshBlastZoneFirstExpiry(blastZone);
                        }))
                        .toList();
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                page++;
                totalProcessed += blastZones.size();
                blastZoneService.saveBlastZones(blastZones);
                log.info("ReProcessed first expiry for {} blast zones", totalProcessed);
            }
            log.info("Recomputation of first expiry for blast zones completed");
        } catch (Exception e) {
            log.error("Error recomputing first expiry for blast zones: {}", e.getMessage(), e);
        }
    }


}

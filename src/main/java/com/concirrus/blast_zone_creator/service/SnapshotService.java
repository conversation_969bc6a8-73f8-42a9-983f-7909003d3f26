package com.concirrus.blast_zone_creator.service;


import com.concirrus.blast_zone_creator.dal.SnapshotJobRepository;
import com.concirrus.blast_zone_creator.dal.blastzone.BlastZoneRepository;
import com.concirrus.blast_zone_creator.dto.enums.ProcessingStatus;
import com.concirrus.blast_zone_creator.model.SnapshotJob;
import com.concirrus.blast_zone_creator.model.blastzone.BlastZone;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Service
@Slf4j
@RequiredArgsConstructor
public class SnapshotService {

    public static final String BLAST_ZONE_ = "blast_zone_";
    private final BlastZoneRepository blastZoneRepository;
    private final SnapshotJobRepository snapshotJobRepository;
    private final MongoTemplate mongoTemplate;


    public void createSnapshotJobs() {
        List<SnapshotJob> jobs = new ArrayList<>();
        LocalDate tenthDayOfCurrentMonth = getTenthDayOfCurrentMonth();
        SnapshotJob monthlyJob = new SnapshotJob();
        monthlyJob.setJobId(UUID.randomUUID().toString());
        monthlyJob.setCreatedAt(Instant.now());
        monthlyJob.setExecutionDate(tenthDayOfCurrentMonth);
        jobs.add(monthlyJob);

        for (LocalDate monday : getAllMondaysInCurrentMonth()) {
            SnapshotJob weeklyJob = new SnapshotJob();
            weeklyJob.setJobId(UUID.randomUUID().toString());
            weeklyJob.setCreatedAt(Instant.now());
            weeklyJob.setExecutionDate(monday);
            jobs.add(weeklyJob);
        }
        snapshotJobRepository.saveAll(jobs);
    }

    public void createSnapshot(SnapshotJob job){
        final int TOTAL_RECORDS = 10000;
        final int PAGE_SIZE = 200;
        final int TOTAL_PAGES = TOTAL_RECORDS / PAGE_SIZE;
        final String collectionName = BLAST_ZONE_ + job.getExecutionDate();
        Instant start = Instant.now();
        log.info("Starting migration of top {} blast zones with page size {}", TOTAL_RECORDS, PAGE_SIZE);

        int totalInserted = 0;

        // Loop through pages
        for (int pageNumber = 0; pageNumber < TOTAL_PAGES; pageNumber++) {
            try {
                log.info("Processing page {} of {}", pageNumber + 1, TOTAL_PAGES);

                // Create pageable with sorting by exposure (descending)
                Pageable pageable = PageRequest.of(pageNumber, PAGE_SIZE,
                        Sort.by(Sort.Direction.DESC, "exposure"));

                // Fetch the page
                Page<BlastZone> page = blastZoneRepository.findTopBlastZonesByExposureGreaterThanZero(pageable);

                if (page.hasContent()) {
                    List<BlastZone> pageContent = page.getContent();
                    log.info("Retrieved {} blast zones from page {}", pageContent.size(), pageNumber + 1);

                    insertBlastZonesToNewCollection(pageContent, pageNumber, collectionName);
                    totalInserted += pageContent.size();
                } else {
                    log.warn("No content found for page {}", pageNumber + 1);
                    break; // No more data available
                }

                // small delay to avoid overwhelming the database
                Thread.sleep(1000);

            } catch (Exception e) {
                log.error("Error processing page {}: {}", pageNumber + 1, e.getMessage(), e);
            }
        }
        Instant end = Instant.now();
        int totalSeconds = (int) Duration.between(start, end).toSeconds();
        job.setUpdatedAt(Instant.now());
        job.setTimeTaken(totalInserted);
        job.setStatus(ProcessingStatus.COMPLETED);
        snapshotJobRepository.save(job);
        log.info("Migration completed. Total blast zones processed: {} in {}", totalInserted, totalSeconds);
    }

    private LocalDate getTenthDayOfCurrentMonth() {
        LocalDate now = LocalDate.now();
        return now.withDayOfMonth(10);
    }

    /**
     * Gets all Mondays in the current month
     */
    private List<LocalDate> getAllMondaysInCurrentMonth() {
        LocalDate now = LocalDate.now();
        LocalDate firstDayOfMonth = now.withDayOfMonth(1);
        LocalDate lastDayOfMonth = now.withDayOfMonth(now.lengthOfMonth());

        List<LocalDate> mondays = new ArrayList<>();

        // Find the first Monday of the month

        // Add all Mondays in the month
        LocalDate currentMonday = firstDayOfMonth.with(TemporalAdjusters.nextOrSame(DayOfWeek.MONDAY));
        while (!currentMonday.isAfter(lastDayOfMonth)) {
            mondays.add(currentMonday);
            currentMonday = currentMonday.plusWeeks(1);
        }

        return mondays;
    }

    private void insertBlastZonesToNewCollection(List<BlastZone> blastZones, int batchNumber,String collectionName) {
        try {
            // Create a new collection name (you can customize this)

            // Insert into new collection
            mongoTemplate.insert(blastZones, collectionName);

            log.info("Successfully inserted {} blast zones into {} collection (batch {})",
                    blastZones.size(), collectionName, batchNumber + 1);

        } catch (Exception e) {
            log.error("Error inserting blast zones into new collection for batch {}: {}",
                    batchNumber + 1, e.getMessage(), e);
            throw new RuntimeException("Failed to insert batch " + (batchNumber + 1), e);
        }
    }
}

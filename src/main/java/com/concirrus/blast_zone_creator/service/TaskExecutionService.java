package com.concirrus.blast_zone_creator.service;

import com.concirrus.blast_zone_creator.config.TenantContextHolder;
import com.concirrus.blast_zone_creator.dal.blastzone.BlastZoneDAL;
import com.concirrus.blast_zone_creator.dal.blastzone.BlastZoneLocationMappingDal;
import com.concirrus.blast_zone_creator.dal.miAnalysis.MiAnalysisJobRepository;
import com.concirrus.blast_zone_creator.dal.miAnalysis.MiAnalysisResultRepository;
import com.concirrus.blast_zone_creator.dal.miAnalysis.MiAnalysisTaskRepository;
import com.concirrus.blast_zone_creator.dto.enums.ProcessingStatus;
import com.concirrus.blast_zone_creator.dto.miAnalysis.AnalysisPML;
import com.concirrus.blast_zone_creator.dto.miAnalysis.MiAnalysisJobRequest;
import com.concirrus.blast_zone_creator.model.blastzone.BlastZone;
import com.concirrus.blast_zone_creator.model.miAnalysis.MiAnalysisJob;
import com.concirrus.blast_zone_creator.model.miAnalysis.MiAnalysisResult;
import com.concirrus.blast_zone_creator.model.miAnalysis.MiAnalysisTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import static com.concirrus.blast_zone_creator.config.Constants.*;
import static com.concirrus.blast_zone_creator.utils.AttributeComputationUtils.getGrade;
import static com.concirrus.blast_zone_creator.utils.AttributeComputationUtils.getPercentage;


@Service

@Slf4j
public class TaskExecutionService {
    private final MiAnalysisTaskRepository miAnalysisTaskRepository;
    private final MiAnalysisJobRepository miAnalysisJobRepository;
    private final AttributeComputationService attributeComputationService;
    private final BlastZoneLocationMappingDal locationMappingDal;
    private final BlastZoneDAL blastZoneDAL;
    private final MiAnalysisResultRepository resultRepository;
    private final ExecutorService executorService;

    public TaskExecutionService(MiAnalysisTaskRepository miAnalysisTaskRepository, MiAnalysisJobRepository miAnalysisJobRepository, AttributeComputationService attributeComputationService, BlastZoneLocationMappingDal locationMappingDal, BlastZoneDAL blastZoneDAL, MiAnalysisResultRepository resultRepository,  @Qualifier("virtualThreadExecutor") ExecutorService executorService) {
        this.miAnalysisTaskRepository = miAnalysisTaskRepository;
        this.miAnalysisJobRepository = miAnalysisJobRepository;
        this.attributeComputationService = attributeComputationService;
        this.locationMappingDal = locationMappingDal;
        this.blastZoneDAL = blastZoneDAL;
        this.resultRepository = resultRepository;
        this.executorService = executorService;
    }

    @Async
    public void processJob(String jobId) {
        Optional<MiAnalysisJob> optionalJob = miAnalysisJobRepository.findById(jobId);
        if (optionalJob.isEmpty()) {
            log.error("Job with ID {} not found", jobId);
            return;
        }
        MiAnalysisJob job = optionalJob.get();
        log.info("Processing job with ID: {}", jobId);
        if (job.getStatus() == ProcessingStatus.COMPLETED||job.getStatus() == ProcessingStatus.FAILED||job.getStatus() == ProcessingStatus.IN_PROGRESS) {
            log.info("Job with ID {} is already completed or failed or in progress", jobId);
            return;
        }
        job.setStatus(ProcessingStatus.IN_PROGRESS);
        miAnalysisJobRepository.save(job);
        cleanPreviousResultsBeforeExecution(job);
        List<MiAnalysisTask> taskList = miAnalysisTaskRepository.findByJobId(jobId);
        if (taskList.isEmpty()) {
            log.warn("No tasks found for job ID: {}", jobId);
            return;
        }
        log.info("Found {} tasks for job ID: {}", taskList.size(), jobId);
        for (MiAnalysisTask task : taskList) {
            String submissionId = job.getSubmissionId();
            String quoteId = job.getQuoteId();
            log.info("Processing task with ID: {} for submission ID: {} and quote ID: {}", task.getTaskId(), submissionId, quoteId);
            // Execute the task
            executeTask(submissionId, quoteId, task);
        }
    }
    @Async
    public void createTasks(MiAnalysisJob job, MiAnalysisJobRequest request) {
        for (String peril : request.getPerils()) {
            switch (peril.toLowerCase()) {
                case SRCC:
                    //TODO this will be implemented in later stages
                    log.info("SRCC peril select skipping");
                    break;
                case WAR:
                    //TODO this will be implemented in later stages
                    log.info("WAR peril select skipping");
                    break;
                case SANDT: {
                    MiAnalysisTask  task = MiAnalysisTask.builder().status(ProcessingStatus.IN_PROGRESS).taskId(job.getJobId() + SEPARATOR + peril).limit(request.getSAndT()).deductible(request.getDeductible()).excess(request.getExcess()).jobId(job.getJobId()).line(request.getLine()).createdAt(Instant.now()).build();
                    miAnalysisTaskRepository.save(task);
                    break;
                }
                default:
                    log.info("Invalid peril for MI analysis");
            }
        }
    }

    private void executeTask(String submissionId, String quoteId, MiAnalysisTask task) {
        // Implement the logic to execute a single task
        log.info("Executing task with ID: {}", task.getTaskId());

        // Capture tenant context before async operations
        String tenantId = TenantContextHolder.getTenantId();
        String tenantAlias = TenantContextHolder.getTenantAlias();

        try {
            List<Long> blastZoneIds =  locationMappingDal.findBlastZoneIdsBySubmissionId(submissionId);
            log.info("Found {} blast zones for submission ID: {}", blastZoneIds.size(), submissionId);

            List<CompletableFuture<Void>> futures = blastZoneDAL.stream(blastZoneIds)
                    .map(blastZone -> CompletableFuture.runAsync(() -> {
                        try {
                            // Set tenant context in async thread
                            if (tenantId != null) {
                                TenantContextHolder.setTenantContext(tenantId, tenantAlias);
                            }
                            computeMarginalImpact(task, submissionId, quoteId, blastZone);
                        } finally {
                            // Clean up tenant context in async thread
                            TenantContextHolder.clear();
                        }
                    }))
                    .toList();

            // Wait for all futures to complete
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            log.info("Computation of extended attributes of blast zones completed");
            task.setStatus(ProcessingStatus.COMPLETED);
        } catch (Exception e){
            log.info("ERROR OCCURRED WHILE EXECUTING TASK: {} ... marking as failed", task.getTaskId(), e);
            task.setStatus(ProcessingStatus.FAILED);
        }finally {
            // Save the task status
            task.setUpdatedAt(Instant.now());
            miAnalysisTaskRepository.save(task);
            log.info("Task with ID: {} has been processed with status: {}", task.getTaskId(), task.getStatus());
            // Update the job status if all tasks are completed
            String jobId = task.getJobId();
            List<MiAnalysisTask> pendingTasks = miAnalysisTaskRepository.findByJobIdAndStatus(jobId, ProcessingStatus.IN_PROGRESS);
            if (pendingTasks.isEmpty()) {
                MiAnalysisJob job = miAnalysisJobRepository.findById(jobId).orElse(null);
                if (job != null) {
                    job.setUpdatedAt(Instant.now());
                    job.setStatus(ProcessingStatus.COMPLETED);
                    miAnalysisJobRepository.save(job);
                    log.info("All tasks completed for job ID: {}. Job status updated to COMPLETED.", jobId);
                }
            } else {
                log.info("There are still {} tasks pending for job ID: {}", pendingTasks.size(), jobId);
            }
        }


    }

    private void computeMarginalImpact(
            MiAnalysisTask task,
            String submissionId,
            String quoteId,
            BlastZone blastZone
    ) {
        MiAnalysisResult analysisResult = new MiAnalysisResult();
        analysisResult.setTaskId(task.getTaskId());
        analysisResult.setBlastZoneId(String.valueOf(blastZone.getId()));
        analysisResult.setBlastZoneName(blastZone.getName());

        // --- PML Computation ---
        AnalysisPML analysisPML = attributeComputationService.computePMLForSubmission(blastZone, submissionId);

        double updatedPML = blastZone.getPml() + analysisPML.getPml();
        double updatedGeoResolution = Math.min(blastZone.getGeocodingResolution(), analysisPML.getGeoCodingResolution());
        String updatedGrade = getGrade(updatedGeoResolution);
        double updatedPMLUtilization = getPercentage(updatedPML, PML_UTILIZATION_LIMIT);

        analysisResult.setCurrentPmlContribution(blastZone.getPml());
        analysisResult.setCurrentPmlUtilisation(blastZone.getPmlUtilisation());
        analysisResult.setUpdatedPmlExposure(updatedPML);
        analysisResult.setUpdatedPmlUtilisation(updatedPMLUtilization);
        analysisResult.setUpdatedGeocodingResolution(updatedGeoResolution);
        analysisResult.setUpdatedGeocodingGrade(updatedGrade);

        // --- Exposure Computation ---
        double currentExposure = blastZone.getExposure() != null ? blastZone.getExposure() : 0.0;
        double currentExposureUtilization = blastZone.getDfUtilisation() != null ? blastZone.getDfUtilisation() : 0.0;
        double newExposure = attributeComputationService.computeExposureForSubmission(blastZone, task, submissionId, quoteId);
        double updatedExposure = currentExposure + newExposure;
        double updatedExposureUtilization = getPercentage(updatedExposure, EXPOSURE_UTILIZATION_LIMIT);

        analysisResult.setCurrentExposure(currentExposure);
        analysisResult.setCurrentExposureUtilisation(currentExposureUtilization);
        analysisResult.setUpdatedExposure(updatedExposure);
        analysisResult.setUpdatedExposureUtilisation(updatedExposureUtilization);

        analysisResult.setAccountContribution(updatedExposure - currentExposure);
        resultRepository.save(analysisResult);
    }

    public void cleanPreviousResultsBeforeExecution(MiAnalysisJob currentJob) {

        try {
            // Step 1: Find the previous job for the same submission
            Optional<MiAnalysisJob> optionalPreviousJob = miAnalysisJobRepository.findTopBySubmissionIdAndQuoteIdAndCreatedAtBeforeOrderByCreatedAtDesc(
                    currentJob.getSubmissionId(),
                    currentJob.getQuoteId(),
                    currentJob.getCreatedAt()
            );

            if (optionalPreviousJob.isEmpty()) {
                return; // No previous job exists, so nothing to clean
            }

            MiAnalysisJob previousJob = optionalPreviousJob.get();

            // Step 2: Get all tasks for previous and current jobs
            List<MiAnalysisTask> previousTasks = miAnalysisTaskRepository.findByJobId(previousJob.getJobId());
            List<MiAnalysisTask> currentTasks = miAnalysisTaskRepository.findByJobId(currentJob.getJobId());

            // Step 3: Build a map from peril to taskId for previous job
            Map<String, String> perilToTaskIdMap = previousTasks.stream()
                    .collect(Collectors.toMap(MiAnalysisTask::getPeril, MiAnalysisTask::getTaskId, (a, b) -> a));

            // Step 4: For each current task, check if peril exists in previous and delete the result
            for (MiAnalysisTask currentTask : currentTasks) {
                String peril = currentTask.getPeril();
                if (perilToTaskIdMap.containsKey(peril)) {
                    String oldTaskId = perilToTaskIdMap.get(peril);
                    resultRepository.deleteByTaskId(oldTaskId);
                }
            }
        } catch (Exception e){
            log.error("Error occurred while cleaning previous results", e);
        }
    }
}

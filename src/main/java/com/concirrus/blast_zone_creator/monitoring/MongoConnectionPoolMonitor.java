package com.concirrus.blast_zone_creator.monitoring;

import com.mongodb.event.ConnectionPoolClearedEvent;
import com.mongodb.event.ConnectionPoolClosedEvent;
import com.mongodb.event.ConnectionPoolCreatedEvent;
import com.mongodb.event.ConnectionPoolListener;
import com.mongodb.event.ConnectionCheckedInEvent;
import com.mongodb.event.ConnectionCheckedOutEvent;
import com.mongodb.event.ConnectionCreatedEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * MongoDB Connection Pool Monitor for Multitenant Setup
 * 
 * Monitors connection pool metrics across all tenants to help optimize
 * pool settings and identify potential issues.
 * 
 * Key Metrics Tracked:
 * - Active connections (checked out)
 * - Total connections created
 * - Pool events (created, cleared, closed)
 * - Connection lifecycle events
 */
@Slf4j
@Component
public class MongoConnectionPoolMonitor implements ConnectionPoolListener {

    private final AtomicInteger activeConnections = new AtomicInteger(0);
    private final AtomicLong totalConnectionsCreated = new AtomicLong(0);
    private final AtomicInteger poolsCreated = new AtomicInteger(0);
    private final AtomicInteger poolsCleared = new AtomicInteger(0);
    private final AtomicInteger poolsClosed = new AtomicInteger(0);

    @Override
    public void connectionPoolCreated(ConnectionPoolCreatedEvent event) {
        int pools = poolsCreated.incrementAndGet();
        log.info("MongoDB connection pool created for server: {} | Total pools: {}", 
                event.getServerId(), pools);
        log.info("Pool settings - Max size: {}, Min size: {}", 
                event.getSettings().getMaxSize(), 
                event.getSettings().getMinSize());
    }

    @Override
    public void connectionPoolCleared(ConnectionPoolClearedEvent event) {
        int cleared = poolsCleared.incrementAndGet();
        log.warn("MongoDB connection pool cleared for server: {} | Total cleared: {}", 
                event.getServerId(), cleared);
    }

    @Override
    public void connectionPoolClosed(ConnectionPoolClosedEvent event) {
        int closed = poolsClosed.incrementAndGet();
        log.info("MongoDB connection pool closed for server: {} | Total closed: {}", 
                event.getServerId(), closed);
    }

    @Override
    public void connectionCreated(ConnectionCreatedEvent event) {
        long total = totalConnectionsCreated.incrementAndGet();
        log.debug("New MongoDB connection created: {} | Total connections created: {}", 
                event.getConnectionId(), total);
    }

    @Override
    public void connectionCheckedOut(ConnectionCheckedOutEvent event) {
        int active = activeConnections.incrementAndGet();
        log.debug("Connection checked out: {} | Active connections: {}", 
                event.getConnectionId(), active);
        
        // Log warning if we're using too many connections
        if (active > 150) { // 75% of max pool size (200)
            log.warn("High connection usage detected! Active connections: {} (75%+ of pool)", active);
        }
    }

    @Override
    public void connectionCheckedIn(ConnectionCheckedInEvent event) {
        int active = activeConnections.decrementAndGet();
        log.debug("Connection checked in: {} | Active connections: {}", 
                event.getConnectionId(), active);
    }

    /**
     * Get current connection pool metrics
     */
    public ConnectionPoolMetrics getMetrics() {
        return new ConnectionPoolMetrics(
                activeConnections.get(),
                totalConnectionsCreated.get(),
                poolsCreated.get(),
                poolsCleared.get(),
                poolsClosed.get()
        );
    }

    /**
     * Log current pool status - useful for monitoring
     */
    public void logCurrentStatus() {
        ConnectionPoolMetrics metrics = getMetrics();
        log.info("=== MongoDB Connection Pool Status ===");
        log.info("Active connections: {}", metrics.getActiveConnections());
        log.info("Total connections created: {}", metrics.getTotalConnectionsCreated());
        log.info("Pools created: {}", metrics.getPoolsCreated());
        log.info("Pools cleared: {}", metrics.getPoolsCleared());
        log.info("Pools closed: {}", metrics.getPoolsClosed());
        log.info("=====================================");
    }

    /**
     * Connection pool metrics data class
     */
    public static class ConnectionPoolMetrics {
        private final int activeConnections;
        private final long totalConnectionsCreated;
        private final int poolsCreated;
        private final int poolsCleared;
        private final int poolsClosed;

        public ConnectionPoolMetrics(int activeConnections, long totalConnectionsCreated, 
                                   int poolsCreated, int poolsCleared, int poolsClosed) {
            this.activeConnections = activeConnections;
            this.totalConnectionsCreated = totalConnectionsCreated;
            this.poolsCreated = poolsCreated;
            this.poolsCleared = poolsCleared;
            this.poolsClosed = poolsClosed;
        }

        public int getActiveConnections() { return activeConnections; }
        public long getTotalConnectionsCreated() { return totalConnectionsCreated; }
        public int getPoolsCreated() { return poolsCreated; }
        public int getPoolsCleared() { return poolsCleared; }
        public int getPoolsClosed() { return poolsClosed; }

        @Override
        public String toString() {
            return String.format("ConnectionPoolMetrics{active=%d, totalCreated=%d, poolsCreated=%d, poolsCleared=%d, poolsClosed=%d}",
                    activeConnections, totalConnectionsCreated, poolsCreated, poolsCleared, poolsClosed);
        }
    }
}

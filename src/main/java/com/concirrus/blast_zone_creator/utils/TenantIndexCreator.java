package com.concirrus.blast_zone_creator.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.IndexOperations;
import org.springframework.data.mongodb.core.index.IndexResolver;
import org.springframework.data.mongodb.core.index.MongoPersistentEntityIndexResolver;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;
import org.springframework.data.mongodb.core.mapping.MongoPersistentEntity;
import org.springframework.stereotype.Component;

import java.util.Collection;

@Component
@Slf4j
public class TenantIndexCreator {

    private final MongoMappingContext mappingContext;

    public TenantIndexCreator(MongoMappingContext mappingContext) {
        this.mappingContext = mappingContext;
    }

    public void ensureIndexes(MongoTemplate tenantTemplate) {
        IndexResolver resolver = new MongoPersistentEntityIndexResolver(mappingContext);

        Collection<MongoPersistentEntity<?>> collections = mappingContext.getPersistentEntities();
        log.info("Ensuring indexes for {} collections", collections.size());

        for (MongoPersistentEntity<?> entity : collections) {
            log.info("Ensuring indexes for collection: {}", entity.getType().getSimpleName());
            Class<?> entityType = entity.getType();
            if (!entityType.isAnnotationPresent(Document.class)) {
                log.warn("Entity {} is not a document. Skipping index creation.", entityType.getSimpleName());
                continue;
            }
            IndexOperations indexOps = tenantTemplate.indexOps(entityType);
            resolver.resolveIndexFor(entityType).forEach(indexOps::ensureIndex);
        }
    }
}

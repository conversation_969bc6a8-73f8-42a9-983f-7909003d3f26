package com.concirrus.blast_zone_creator.utils;

import org.springframework.data.mongodb.core.geo.GeoJsonPoint;

public class DistanceUtils {

    private static final int EARTH_RADIUS_METERS = 6371000; // Earth's radius in meters

    /**
     * Calculates the distance between two GeoJsonPoint objects using the Haversine formula
     *
     * @param point1 First GeoJsonPoint (longitude, latitude)
     * @param point2 Second GeoJsonPoint (longitude, latitude)
     * @return Distance in meters
     */
    public static double calculateDistanceInMeters(GeoJsonPoint point1, GeoJsonPoint point2) {
        // Convert to radians (GeoJsonPoint stores as [longitude, latitude])
        double lat1 = Math.toRadians(point1.getY());
        double lon1 = Math.toRadians(point1.getX());
        double lat2 = Math.toRadians(point2.getY());
        double lon2 = Math.toRadians(point2.getX());

        // Haversine formula
        double dLat = lat2 - lat1;
        double dLon = lon2 - lon1;
        double a = Math.pow(Math.sin(dLat / 2), 2) +
                Math.cos(lat1) * Math.cos(lat2) *
                        Math.pow(Math.sin(dLon / 2), 2);

        return 2 * EARTH_RADIUS_METERS * Math.asin(Math.sqrt(a));
    }
}

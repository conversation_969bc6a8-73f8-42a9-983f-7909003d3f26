package com.concirrus.blast_zone_creator.utils;

import com.concirrus.blast_zone_creator.config.TenantContextHolder;
import com.concirrus.blast_zone_creator.service.TenantService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import static com.concirrus.blast_zone_creator.constants.Constants.CLIENT_ID;


@Component
@RequiredArgsConstructor
public class TenantInterceptor implements HandlerInterceptor {

    @Autowired
    private final TenantService tenantService;

    @Override
    public boolean preHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) {
        // Extract tenant identifier from request (header, path, etc.)
        String tenantIdentifier = extractTenantIdentifier(request);

        if (tenantIdentifier != null) {
            String tenantAlias = tenantService.getTenantAlias(tenantIdentifier);
            if (tenantAlias != null) {
                TenantContextHolder.setTenantContext(tenantIdentifier, tenantAlias);
            }else{
                throw new RuntimeException("Tenant alias is null for client ID " + tenantIdentifier);
            }
        }else{
            throw new RuntimeException("Client ID is null in request");
        }

        return true;
    }

    @Override
    public void afterCompletion(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler, Exception ex) {
        TenantContextHolder.clear();
    }
    private String extractTenantIdentifier(HttpServletRequest request) {
        // Option 1: From header
        String tenantId = request.getHeader(CLIENT_ID);

        // Option 2: From path parameter
        if (tenantId == null) {
            String path = request.getRequestURI();
            // Extract from path like /api/v1/tenant/{tenantId}/...
            if (path.contains("/tenant/")) {
                String[] parts = path.split("/");
                for (int i = 0; i < parts.length - 1; i++) {
                    if ("tenant".equals(parts[i])) {
                        tenantId = parts[i + 1];
                        break;
                    }
                }
            }
        }

        // Option 3: From subdomain
        if (tenantId == null) {
            String host = request.getHeader("Host");
            if (host != null && host.contains(".")) {
                tenantId = host.substring(0, host.indexOf("."));
            }
        }

        return tenantId;
    }

}
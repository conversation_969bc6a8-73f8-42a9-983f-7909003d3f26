package com.concirrus.blast_zone_creator.utils;

import com.concirrus.blast_zone_creator.model.Account;
import com.concirrus.blast_zone_creator.model.DamageFactor;
import com.concirrus.blast_zone_creator.model.Location;
import com.concirrus.blast_zone_creator.model.blastzone.BlastZone;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.concirrus.blast_zone_creator.config.Constants.*;

public class AttributeComputationUtils {

    /**
     * Partition a list into smaller chunks
     */
    public static <T> List<List<T>> partitionList(List<T> list, int chunkSize) {
        List<List<T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += chunkSize) {
            partitions.add(list.subList(i, Math.min(i + chunkSize, list.size())));
        }
        return partitions;
    }

    public static double calculateAndSetExposure(Account acc) {
        double calculatedExposure = calculateAccountExposure(acc);
        double line = acc.getLine() != null ? acc.getLine() : 100.0;
        double adjustedExposure = calculatedExposure * (line / 100.0);
        acc.setExposure(adjustedExposure);
        return adjustedExposure;
    }

    /**
     * Calculates the exposure for a single account based on TIV, limit, excess, and deductible
     */
    public static double  calculateAccountExposure(Account acc) {
        double tiv = acc.getTiv();
        double limit = acc.getLimit();
        double excess = acc.getExcess();
        double deductible = acc.getDeductible();

        double excessPlusDeductible = excess + deductible;

        if (tiv > (limit + excessPlusDeductible)) {
            return limit;
        } else if (tiv <= excessPlusDeductible) {
            return 0.0;
        } else {
            return Math.round(tiv - excessPlusDeductible);
        }
    }

    public static String determineRelevantExpiry(List<String> expiryDates) {
        LocalDate today = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        LocalDate earliestFuture = null;
        LocalDate closestPast = null;

        for (String dateStr : expiryDates) {
            try {
                LocalDate date = LocalDate.parse(dateStr, formatter);

                if (!date.isBefore(today)) {
                    if (earliestFuture == null || date.isBefore(earliestFuture)) {
                        earliestFuture = date;
                    }
                } else {
                    if (closestPast == null || ChronoUnit.DAYS.between(date, today) <
                            ChronoUnit.DAYS.between(closestPast, today)) {
                        closestPast = date;
                    }
                }

            } catch (DateTimeParseException e) {
               System.out.println("Invalid date format: " + dateStr);
            }
        }

        LocalDate finalDate = earliestFuture != null ? earliestFuture : closestPast;
        return finalDate != null ? finalDate.format(formatter) : null;
    }

    public static double getPml(Double contentsValue, Double biValue, Double buildingValue, DamageFactor damageFactor) {

        double contentDamageValue = contentsValue * damageFactor.getContentsDf() / HUNDRED;
        double biDamageValue = biValue * damageFactor.getBiDamage() / HUNDRED;
        double buildingDamageValue = buildingValue * damageFactor.getBuildingDf() / HUNDRED;
        return contentDamageValue + biDamageValue + buildingDamageValue;
    }


    public static Map<String, Double> getLocationDistanceMap(
            BlastZone blastZone,
            List<Location> locationsWithin) {

        Map<String, Double> locationToDistanceMap = new HashMap<>();
        for (Location location : locationsWithin) {
            double distance = DistanceUtils.calculateDistanceInMeters(
                    blastZone.getCentre(), location.getGeometry()
            );

            locationToDistanceMap.put(location.getId(), distance);
        }

        return locationToDistanceMap;
    }

    public static String getGrade(Double resolution){
        if (resolution == null || resolution < ZERO) {
            return BAD;
        }
        if (resolution>80){
            return GOOD;
        } else if (resolution>=65) {
            return IDEAL;
        }else {
            return BAD;
        }
    }

    public static double getPercentage(double value, double base){
        if  ( base ==ZERO) {
            return ZERO;
        }
        return (value / base) * HUNDRED;
    }

    public static boolean hasValidFields(Account acc) {
        return acc.getTiv() != null &&
                acc.getLimit() != null &&
                acc.getExcess() != null &&
                acc.getDeductible() != null;
    }

}

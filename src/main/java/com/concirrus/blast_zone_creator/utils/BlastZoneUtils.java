package com.concirrus.blast_zone_creator.utils;


import com.concirrus.blast_zone_creator.config.GeoJsonSerializersModule;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.uber.h3core.H3Core;
import com.uber.h3core.util.LatLng;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.util.GeometricShapeFactory;
import org.springframework.data.geo.Point;
import org.springframework.data.mongodb.core.geo.GeoJsonPoint;
import org.springframework.data.mongodb.core.geo.GeoJsonPolygon;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
public class BlastZoneUtils {

    private static final ObjectMapper mapper = new ObjectMapper();
    private static final H3Core h3;
    private static final int H3_RES = 11;

    private static final int KRINGS = 4;

    private static final GeometryFactory geometryFactory = new GeometryFactory();

    static {
        try {
            h3 = H3Core.newInstance();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        mapper.registerModule(new GeoJsonSerializersModule());
    }

    public static void main(String[] args) throws Exception{
        var points = getBlastZoneCentres(0, 0);
        System.out.println(points.size());
    }

    public static Set<Point> getBlastZoneCentres(
            double lat,
            double lng) throws Exception {

        var hexId = h3.latLngToCell(lat, lng, H3_RES);

        var krings = h3.gridDisk(hexId, KRINGS);
        log.debug("We have {} K-RINGS around the point", krings.size());

        Set<Point> blastZoneCentres = new HashSet<>();

        for(var kring: krings) {
            blastZoneCentres.addAll(h3ToPolygonV2(kring));
        }

        return blastZoneCentres;
    }

    private static List<Point> h3ToPolygonV2(long hexId) {
        var latLngs = h3.cellToBoundary(hexId);
        List<Point> points = new ArrayList<>();
        for(var latlng : latLngs) {
            points.add(new Point(latlng.lng, latlng.lat));
        }

        return points;
    }

    public static GeoJsonPolygon getBlastZone(Point blastZoneCentre) {
        var bzCentre = geometryFactory.createPoint(new Coordinate(blastZoneCentre.getX(), blastZoneCentre.getY()));
        Geometry bzCircle = createCircle(bzCentre, 250, geometryFactory);
        GeoJsonPolygon bzPoly = fromJTSGeometry(bzCircle);
        return bzPoly;
    }

    public static GeoJsonPolygon getCircle(GeoJsonPoint center, double radius) {
        return getCircle(new Point(center.getX(), center.getY()), radius);
    }

    public static GeoJsonPolygon getCircle(Point center, double radius) {
        var bzCentre = geometryFactory.createPoint(new Coordinate(center.getX(), center.getY()));
        Geometry bzCircle = createCircle(bzCentre, radius, geometryFactory);
        GeoJsonPolygon bzPoly = fromJTSGeometry(bzCircle);
        return bzPoly;
    }



    private static GeoJsonPolygon fromJTSGeometry(Geometry geometry) {
        List<Point> points = new ArrayList<>();
        for(var point : geometry.getCoordinates()) {
            points.add(new Point(point.x, point.y));
        }
        return new GeoJsonPolygon(points);
    }

    private static List<Long> getHexagonsForCircle(Geometry geometry) {
        List<LatLng> latLngs = new ArrayList<>();
        for(var coor: geometry.getCoordinates()) {
            latLngs.add(new LatLng(coor.y, coor.x));
        }
        return h3.polygonToCells(latLngs, null, H3_RES);
    }

    /**
     * Creates a circle around a point.
     * Note: This creates a regular polygon approximation of a circle with 32 segments.
     *
     * @param center The center point
     * @param radiusMeters The radius in meters
     * @param geometryFactory The geometry factory
     * @return A polygon approximating a circle
     */
    private static Geometry createCircle(org.locationtech.jts.geom.Point center, double radiusMeters, GeometryFactory geometryFactory) {
        // For simplicity, we're using a rough approximation of meters to degrees
        // This is reasonable for small distances but becomes less accurate for larger ones
        // 1 degree of latitude is approximately 111.32 km
        // 1 degree of longitude varies based on latitude
        double radiusDegrees = radiusMeters / 111320.0;

        // For more accurate transformations, consider using libraries like Proj4J or
        // implementing proper spherical geometry calculations

        // Create a shape factory
        GeometricShapeFactory shapeFactory = new GeometricShapeFactory(geometryFactory);
        shapeFactory.setNumPoints(32); // Number of points in the approximated circle
        shapeFactory.setCentre(new Coordinate(center.getX(), center.getY()));

        // Calculate appropriate width and height based on latitude
        // At the equator, 1 degree of longitude is about 111.32 km
        // At latitude α, 1 degree of longitude is about 111.32 * cos(α) km
        double latitudeRadians = Math.toRadians(center.getY());
        double longitudeDegreesPerMeter = 1.0 / (111320.0 * Math.cos(latitudeRadians));

        shapeFactory.setWidth(2 * radiusMeters * longitudeDegreesPerMeter);
        shapeFactory.setHeight(2 * radiusDegrees);

        return shapeFactory.createCircle();
    }


//    private static List<Point> h3ToPolygonV2(long hexId) {
//        var latLngs = h3.cellToBoundary(hexId);
//        List<Point> points = new ArrayList<>();
//        for(var latlng : latLngs) {
//            points.add(new Point(latlng.lng, latlng.lat));
//        }
//
//        return points;
//    }

    private static GeoJsonPolygon h3ToPolygon(long hexId) {
        var latLngs = h3.cellToBoundary(hexId);
        List<Point> points = new ArrayList<>();
        for(var latlng : latLngs) {
            points.add(new Point(latlng.lng, latlng.lat));
        }

        // Close the loop by adding the first point again at the end
        if (!points.isEmpty()) {
            points.add(points.get(0));
        }
        return new GeoJsonPolygon(points);
    }

    public static String asJson(Object obj) throws Exception {
        return mapper.writeValueAsString(obj);
    }

}

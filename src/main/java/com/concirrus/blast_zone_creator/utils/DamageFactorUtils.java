package com.concirrus.blast_zone_creator.utils;

public class DamageFactorUtils {

    public static double getDamageFactor(double distanceInMeters) {
        if (distanceInMeters <= 60) {
            return 100.0;
        } else if (distanceInMeters <= 100) {
            return 80.0;
        } else if (distanceInMeters <= 150) {
            return 65.0;
        } else if (distanceInMeters <= 200) {
            return 35.0;
        } else if (distanceInMeters <= 250) {
            return 20.0;
        } else {
            return 0.0; // Beyond zone 5
        }
    }

    public static String getDamageFactorZone(double distanceInMeters) {
        if (distanceInMeters <= 60) {
            return "Zone A";
        } else if (distanceInMeters <= 100) {
            return "Zone B";
        } else if (distanceInMeters <= 150) {
            return "Zone C";
        } else if (distanceInMeters <= 200) {
            return "Zone D";
        } else if (distanceInMeters <= 250) {
            return "Zone E";
        } else {
            return "INVALID"; // Beyond zone 5
        }
    }

    /**
     * Red (#FF0000) - High priority
     * Orange (#FFA500) - Medium-high priority
     * Yellow (#FFFF00) - Medium priority
     * Light green (#90EE90) - Medium-low priority
     * Green (#008000) - Low priority
     * @param distanceInMeters
     * @return
     */
    public static String getColor(double distanceInMeters) {
        if (distanceInMeters <= 60) {
            return "#FF0000";
        } else if (distanceInMeters <= 100) {
            return "#FFA500";
        } else if (distanceInMeters <= 150) {
            return "#FFFF00";
        } else if (distanceInMeters <= 200) {
            return "#90EE90";
        } else if (distanceInMeters <= 250) {
            return "#008000";
        } else {
            return null; // Beyond zone 5
        }
    }

}
